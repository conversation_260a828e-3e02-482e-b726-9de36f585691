<?xml version="1.0" encoding="UTF-8"?>
<Readable>
    <Reader class="com.boc.mcf.reader.IFFNoLineBreakReader">
        <params>
            <param value="61388" name="encoding"/>
            <!--	<param name="normFileSize" value="10"/>-->
            <!--	<param name="warnFileSize" value="30"/>-->
            <!--	<param name="errorFileSize" value="90000"/>-->
            <param name="callbackClass" value="com.boc.ocs.utils.callback.Dw56CifReadDataCallback"/>
        </params>

        <Parser class="com.boc.mcf.reader.parser.FixByteLengthParser"
                bean="com.boc.ocs.eaihub.jaxb.bean.iff.Dw56CIf">
			<column length="10" property="acdate" regExp="" parseMethod=""/>
			<column length="1" property="deltaFlag" regExp="" parseMethod=""/>
			<column length="6" property="mcif" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="2" property="openBk" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 3 PACKED DECIMAL -->
			<column length="2" property="openBh" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 3 PACKED DECIMAL -->
			<column length="10" property="opnDate" regExp="" parseMethod=""/>
			<column length="10" property="cloDate" regExp="" parseMethod=""/>
			<column length="3" property="brand" regExp="" parseMethod=""/>
			<column length="1" property="waiveMfee" regExp="" parseMethod=""/>
			<column length="2" property="waiveMfeePrd" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 3 PACKED DECIMAL -->
			<column length="8" property="settleAc" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="3" property="settleAccur" regExp="" parseMethod=""/>
			<column length="2" property="protChqAcQty" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 3 PACKED DECIMAL -->
			<column length="2" property="sweepingAcQty" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 3 PACKED DECIMAL -->
			<column length="6" property="mchgExtOuts" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="3" property="mchgcCur1" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt1" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd1" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp1" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive1" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid1" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy1" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes1" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType1" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate1" regExp="" parseMethod=""/>
			<column length="3" property="mchgcCur2" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt2" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd2" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp2" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive2" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid2" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy2" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes2" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType2" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate2" regExp="" parseMethod=""/>
			<column length="3" property="mchgcCur3" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt3" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd3" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp3" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive3" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid3" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy3" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes3" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType3" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate3" regExp="" parseMethod=""/>
			<column length="3" property="mchgcCur4" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt4" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd4" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp4" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive4" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid4" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy4" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes4" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType4" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate4" regExp="" parseMethod=""/>
			<column length="3" property="mchgcCur5" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt5" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd5" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp5" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive5" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid5" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy5" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes5" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType5" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate5" regExp="" parseMethod=""/>
			<column length="3" property="mchgcCur6" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt6" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd6" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp6" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive6" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid6" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy6" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes6" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType6" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate6" regExp="" parseMethod=""/>
			<column length="3" property="mchgcCur7" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt7" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd7" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp7" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive7" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid7" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy7" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes7" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType7" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate7" regExp="" parseMethod=""/>
			<column length="3" property="mchgcCur8" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt8" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd8" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp8" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive8" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid8" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy8" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes8" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType8" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate8" regExp="" parseMethod=""/>
			<column length="3" property="mchgcCur9" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt9" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd9" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp9" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive9" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid9" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy9" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes9" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType9" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate9" regExp="" parseMethod=""/>
			<column length="3" property="mchgcCur10" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt10" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd10" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp10" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive10" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid10" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy10" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes10" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType10" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate10" regExp="" parseMethod=""/>
			<column length="3" property="mchgcCur11" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt11" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd11" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp11" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive11" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid11" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy11" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes11" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType11" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate11" regExp="" parseMethod=""/>
			<column length="3" property="mchgcCur12" regExp="" parseMethod=""/>
			<column length="6" property="mchgcAmt12" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchgcRcvd12" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchgcRp12" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchgcWaive12" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaid12" regExp="" parseMethod=""/>
			<column length="1" property="mchgcPaidBy12" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrTimes12" regExp="" parseMethod=""/>
			<column length="1" property="mchgcLtrType12" regExp="" parseMethod=""/>
			<column length="10" property="mchgcLtrDate12" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur1" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt1" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd1" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp1" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive1" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid1" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy1" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes1" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType1" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate1" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur2" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt2" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd2" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp2" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive2" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid2" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy2" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes2" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType2" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate2" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur3" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt3" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd3" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp3" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive3" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid3" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy3" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes3" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType3" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate3" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur4" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt4" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd4" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp4" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive4" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid4" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy4" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes4" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType4" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate4" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur5" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt5" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd5" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp5" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive5" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid5" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy5" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes5" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType5" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate5" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur6" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt6" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd6" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp6" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive6" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid6" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy6" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes6" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType6" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate6" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur7" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt7" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd7" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp7" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive7" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid7" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy7" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes7" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType7" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate7" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur8" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt8" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd8" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp8" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive8" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid8" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy8" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes8" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType8" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate8" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur9" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt9" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd9" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp9" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive9" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid9" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy9" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes9" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType9" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate9" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur10" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt10" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd10" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp10" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive10" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid10" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy10" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes10" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType10" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate10" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur11" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt11" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd11" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp11" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive11" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid11" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy11" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes11" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType11" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate11" regExp="" parseMethod=""/>
			<column length="3" property="mchglCur12" regExp="" parseMethod=""/>
			<column length="6" property="mchglAmt12" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="6" property="mchglRcvd12" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="8" property="mchglrp12" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 15 PACKED DECIMAL -->
			<column length="1" property="mchglWaive12" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaid12" regExp="" parseMethod=""/>
			<column length="1" property="mchglPaidBy12" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrTimes12" regExp="" parseMethod=""/>
			<column length="1" property="mchglLtrType12" regExp="" parseMethod=""/>
			<column length="10" property="mchglLtrDate12" regExp="" parseMethod=""/>
			<column length="6" property="pcode" regExp="" parseMethod=""/>
			<column length="2" property="flwBh" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 3 PACKED DECIMAL -->
			<column length="4" property="flwOff" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 7 PACKED DECIMAL -->
			<column length="6" property="pbsCif" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="1" property="optIn" regExp="" parseMethod=""/>
			<column length="10" property="chgbrdDate" regExp="" parseMethod=""/>
			<column length="3" property="lstBrand" regExp="" parseMethod=""/>
			<column length="2" property="chgbrdReason" regExp="" parseMethod=""/>
			<column length="140" property="sourceChannel" regExp="" parseMethod=""/>
			<column length="140" property="sourceName" regExp="" parseMethod=""/>
			<column length="1" property="flType" regExp="" parseMethod=""/>
			<column length="6" property="l23OutsBal" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 11 PACKED DECIMAL -->
			<column length="1" property="waivedInfo" regExp="" parseMethod=""/>
			<column length="4" property="openingOfficer" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 7 PACKED DECIMAL -->
			<column length="4" property="rmaOfficerCode" regExp="" parseMethod="com.boc.mcf.conv.PackedDecimalUtils.packDecimalToString"/><!-- 7 PACKED DECIMAL -->
			<column length="1" property="optOut" regExp="" parseMethod=""/>
        </Parser>
    </Reader>
    <ReadableValidator>
         <Validator class="com.boc.mcf.reader.validator.IFFNoLineBreakFileSizeValidator"/>
    </ReadableValidator>
</Readable>
