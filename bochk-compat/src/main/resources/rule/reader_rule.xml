<?xml version="1.0"?>
<digester-rules>
	<pattern value="Readable">
		<object-create-rule classname="com.boc.mcf.reader.config.ReadableConfig" />
		<pattern value="Reader">
			<object-create-rule classname="com.boc.mcf.reader.config.ReaderConfig" />
			<set-properties-rule>
				<alias attr-name="class" prop-name="className" />
			</set-properties-rule>
			<include path="reader/rule/reader_params_rule.xml"/>				
			<pattern value="encoding">
				<call-method-rule></call-method-rule>
			</pattern>
			<pattern value="Parser">
				<object-create-rule classname="com.boc.mcf.reader.config.ParserConfig" />
				<set-properties-rule>
					<alias attr-name="class" prop-name="className" />
					<alias attr-name="bean" prop-name="beanName" />
				</set-properties-rule>
				<pattern value="column">
					<object-create-rule classname="com.boc.mcf.reader.config.ColumnConfig" />
					<set-properties-rule>
						<alias attr-name="length" prop-name="length" />
						<alias attr-name="property" prop-name="property" />
						<alias attr-name="regExp" prop-name="regExp" />
						<alias attr-name="parseMethod" prop-name="parseMethod" />
					</set-properties-rule>
					<set-next-rule methodname="addColumnConfig"/>
				</pattern>
				<set-next-rule methodname="setParserConfig"/>
			</pattern>
			<set-next-rule methodname="setReaderConfig"/>
		</pattern>
		<pattern value="ReadableValidator">
			<pattern value="Validator">
				<object-create-rule classname="com.boc.mcf.reader.config.ReadableValidatorConfig" />
				<set-properties-rule>
					<alias attr-name="class" prop-name="className" />
				</set-properties-rule>
				<include path="reader/rule/reader_params_rule.xml"/>
				<set-next-rule methodname="addReadableValidatorConfig"/>				
			</pattern>
		</pattern>
	</pattern>
</digester-rules>