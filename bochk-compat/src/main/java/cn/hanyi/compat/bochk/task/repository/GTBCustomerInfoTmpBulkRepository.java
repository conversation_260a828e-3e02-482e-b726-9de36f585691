package cn.hanyi.compat.bochk.task.repository;

import cn.hanyi.compat.bochk.task.consts.CustomerInfoFileType;
import cn.hanyi.compat.bochk.task.entity.GTBCustomerInfoTmp;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@RequiredArgsConstructor
public class GTBCustomerInfoTmpBulkRepository {
    private final EntityManager entityManager;
    @Autowired
    JdbcTemplate jdbcTemplate;

    @Transactional(rollbackFor = Exception.class)
    public void bulkUpsert(List<GTBCustomerInfoTmp> entities, CustomerInfoFileType fileType) {
        if (entities.isEmpty()) return;

        String sql = buildUpsertSql(fileType);
        jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                GTBCustomerInfoTmp entity = entities.get(i);
                setPreparedStatementParameters(ps, entity, fileType);
            }

            @Override
            public int getBatchSize() {
                return entities.size();
            }
        });
    }

    private String buildUpsertSql(CustomerInfoFileType fileType) {
        switch (fileType) {
            case BDP_CBS:
                return "INSERT INTO gtb_customer_info_tmp " +
                        "(import_date, cbs_ac, cin_no, cbs_ac_cin_no, bank_code, segment_code, update_count) " +
                        "VALUES (?, ?, ?, ?, ?, ?, 0) " +
                        "ON DUPLICATE KEY UPDATE " +
                        "bank_code = VALUES(bank_code), update_count = update_count + 1";
            case BDP_COMPANY:
                return "INSERT INTO gtb_customer_info_tmp " +
                        "(import_date, cbs_ac, cin_no, cbs_ac_cin_no, bank_code, segment_code, update_count) " +
                        "VALUES (?, ?, ?, ?, ?, ?, 0) " +
                        "ON DUPLICATE KEY UPDATE " +
                        "segment_code = VALUES(segment_code), update_count = update_count + 1";

            case AM_USR:
                return "INSERT INTO gtb_customer_info_tmp " +
                        "(import_date, cbs_ac, user_id, stat_primary, module_id, update_count) " +
                        "VALUES (?, ?, ?, ?, ?, 0) " +
                        "ON DUPLICATE KEY UPDATE " +
                        "user_id = VALUES(user_id), stat_primary = VALUES(stat_primary), update_count = update_count + 1";
            case BDP_PROFILE:
                return "INSERT INTO gtb_customer_info_tmp " +
                        "(import_date, cbs_ac, user_id, stat_primary, module_id, update_count) " +
                        "VALUES (?, ?, ?, ?, ?, 0) " +
                        "ON DUPLICATE KEY UPDATE " +
                        "module_id = VALUES(module_id), update_count = update_count + 1";

            case DW56CIF:
                return "INSERT INTO gtb_customer_info_tmp " +
                        "(import_date, cin_no, customer_manager_id, update_count) " +
                        "VALUES (?, ?, ?, 0) " +
                        "ON DUPLICATE KEY UPDATE " +
                        "customer_manager_id = VALUES(customer_manager_id), update_count = update_count + 1";

            default:
                throw new IllegalArgumentException("Unknown file type: " + fileType);
        }
    }

    private void setQueryParameters(Query query, GTBCustomerInfoTmp entity, CustomerInfoFileType fileType) {
        int paramIndex = 1;

        switch (fileType) {
            case BDP_CBS:
                query.setParameter(paramIndex++, entity.getCbsAC());
                query.setParameter(paramIndex++, entity.getCinNo());
                query.setParameter(paramIndex++, entity.getCbsAcCinNo());
                query.setParameter(paramIndex++, entity.getBankCode());
                query.setParameter(paramIndex++, null); // segment_code for CBS
                query.setParameter(paramIndex, entity.getImportDate());
                break;

            case BDP_COMPANY:
                query.setParameter(paramIndex++, entity.getCbsAC());
                query.setParameter(paramIndex++, entity.getCinNo());
                query.setParameter(paramIndex++, entity.getCbsAcCinNo());
                query.setParameter(paramIndex++, null); // bank_code for COMPANY
                query.setParameter(paramIndex++, entity.getSegmentCode());
                query.setParameter(paramIndex, entity.getImportDate());
                break;

            case AM_USR:
                query.setParameter(paramIndex++, entity.getCbsAC());
                query.setParameter(paramIndex++, entity.getUserId());
                query.setParameter(paramIndex++, entity.getStatPrimary());
                query.setParameter(paramIndex++, null); // module_id for AM_USR
                query.setParameter(paramIndex, entity.getImportDate());
                break;

            case BDP_PROFILE:
                query.setParameter(paramIndex++, entity.getCbsAC());
                query.setParameter(paramIndex++, entity.getUserId());
                query.setParameter(paramIndex++, null); // stat_primary for PROFILE
                query.setParameter(paramIndex++, entity.getModuleId());
                query.setParameter(paramIndex, entity.getImportDate());
                break;

            case DW56CIF:
                query.setParameter(paramIndex++, entity.getCinNo());
                query.setParameter(paramIndex++, entity.getCustomerManagerId());
                query.setParameter(paramIndex, entity.getImportDate());
                break;
            default:
                throw new RuntimeException();
        }
    }

    private void setPreparedStatementParameters(PreparedStatement ps, GTBCustomerInfoTmp entity, CustomerInfoFileType fileType) throws SQLException {
        int paramIndex = 1;

        switch (fileType) {
            case BDP_CBS:
                ps.setDate(paramIndex++, new java.sql.Date(entity.getImportDate().getTime()));
                ps.setString(paramIndex++, entity.getCbsAC());
                ps.setString(paramIndex++, entity.getCinNo());
                ps.setString(paramIndex++, entity.getCbsAcCinNo());
                ps.setString(paramIndex++, entity.getBankCode());
                ps.setString(paramIndex, null);
                break;

            case BDP_COMPANY:
                ps.setDate(paramIndex++, new java.sql.Date(entity.getImportDate().getTime()));
                ps.setString(paramIndex++, entity.getCbsAC());
                ps.setString(paramIndex++, entity.getCinNo());
                ps.setString(paramIndex++, entity.getCbsAcCinNo());
                ps.setString(paramIndex++, null);
                ps.setString(paramIndex, entity.getSegmentCode());
                break;

            case AM_USR:
                ps.setDate(paramIndex++, new java.sql.Date(entity.getImportDate().getTime()));
                ps.setString(paramIndex++, entity.getCbsAC());
                ps.setString(paramIndex++, entity.getUserId());
                ps.setString(paramIndex++, entity.getStatPrimary());
                ps.setString(paramIndex++, null);
                break;

            case BDP_PROFILE:
                ps.setDate(paramIndex++, new java.sql.Date(entity.getImportDate().getTime()));
                ps.setString(paramIndex++, entity.getCbsAC());
                ps.setString(paramIndex++, entity.getUserId());
                ps.setString(paramIndex++, null);
                ps.setString(paramIndex, entity.getModuleId());
                break;

            case DW56CIF:
                ps.setDate(paramIndex++, new java.sql.Date(entity.getImportDate().getTime()));
                ps.setString(paramIndex++, entity.getCinNo());
                ps.setString(paramIndex, entity.getCustomerManagerId());
                break;
            default:
                throw new RuntimeException();
        }
    }
}
