package cn.hanyi.compat.bochk.task.repository;

import cn.hanyi.compat.bochk.task.entity.GtbBlackListData;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface GtbBlackListDataRepository extends ResourceRepository<GtbBlackListData, Long> {
    GtbBlackListData findTopByExternalUserIdOrderByImportDateDesc(String externalUserId);
//    Optional<GtbBlackListData> findFirstByExternalUserIdAndImportDate(String externalUserId, Date importDate);
    List<GtbBlackListData> findAllByExternalUserIdInAndImportDate(Set<String> externalUserIds, Date importDate);
    @Modifying
    @Query(value = "DELETE FROM gtb_blacklist_data WHERE import_date < ?1 LIMIT ?2", nativeQuery = true)
    int deleteBatchByImportDateBefore(Date importDate, int batchSize);

}
