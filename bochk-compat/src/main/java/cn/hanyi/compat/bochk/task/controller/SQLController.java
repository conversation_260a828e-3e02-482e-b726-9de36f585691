package cn.hanyi.compat.bochk.task.controller;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@RestController
@RequestMapping("trigger/data-task/sql")
@PreAuthorize("isAuthenticated()")
@Slf4j
public class SQLController {

    @Value("${bochk.sql.enable:true}")
    private boolean enableSql;
    @Value("${bochk.sql.tables:}")
    private String tables;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @PostMapping("/a4d7c4fa-0fb2-436d-9fa7-7cdfa78549de")
    public ResourceResponseDto<Object> sql(@RequestBody Map<String, String> sql) {
        if (!enableSql || !TenantContext.getCurrentIsAdmin()) {
            throw new BadRequestException(String.format("enableSql=%s, isAdmin=%s", enableSql, TenantContext.getCurrentIsAdmin()));
        }

        String[] supportTables = tables.split(",");
        log.info("support tables:{}-sql: {}", tables, JsonHelper.toJson(sql));

        String query = sql.get("query");
        String update = sql.get("update");
        String insert = sql.get("insert");
        String type = "query";

        boolean throwTable = true;
        for (String table : supportTables) {
            if (StringUtils.isNotEmpty(query) && query.toLowerCase().contains("from " + table)) {
                throwTable = false;
                break;
            }

            if (StringUtils.isNotEmpty(update) && update.toLowerCase().contains("update " + table)) {
                throwTable = false;
                type = "update";
                break;
            }

            if (StringUtils.isNotEmpty(insert) && insert.toLowerCase().contains("insert into " + table)) {
                throwTable = false;
                type = "insert";
                break;
            }
        }

        if (throwTable) {
            throw new BadRequestException("table not support");
        }

        return switch (type) {
            case "query" -> new ResourceResponseDto<>(jdbcTemplate.queryForList(query));
            case "update" -> new ResourceResponseDto<>(jdbcTemplate.update(update));
            case "insert" -> new ResourceResponseDto<>(jdbcTemplate.update(insert));
            default -> throw new BadRequestException("type not support");
        };

    }
}
