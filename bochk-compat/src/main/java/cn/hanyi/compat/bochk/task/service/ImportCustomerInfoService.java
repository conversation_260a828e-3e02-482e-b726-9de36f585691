package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.consts.CustomerInfoFileType;
import cn.hanyi.compat.bochk.task.consts.DataTaskLogStatus;
import cn.hanyi.compat.bochk.task.dto.IFFCustomerInfoImportConfig;
import cn.hanyi.compat.bochk.task.entity.DataTask;
import cn.hanyi.compat.bochk.task.entity.DataTaskLog;
import cn.hanyi.compat.bochk.task.entity.GTBCustomerInfoTmp;
import cn.hanyi.compat.bochk.task.repository.DataTaskLogRepository;
import cn.hanyi.compat.bochk.task.repository.GTBCustomerInfoTmpRepository;
import cn.hanyi.compat.bochk.task.repository.GTBCustomerInfoTmpBulkRepository;
import cn.hanyi.compat.bochk.task.service.impls.IFFDataService;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.repository.CustomerRepository;

import com.alipay.api.domain.Org;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpATTRS;
import com.jcraft.jsch.SftpException;
import java.io.IOException;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ExtendCustomerFieldType;
import org.befun.auth.dto.orgconfig.OrgConfigExtendCustomerFieldDto;
import org.befun.auth.dto.orgconfig.OrgConfigExtendCustomerFieldDto.FieldOption;
import org.befun.auth.entity.OrganizationConfig;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 该服务负责从SFTP拉取客户相关的增量数据文件，解析后批量插入到临时表。
 */
@Service
@EnableAsync
@Slf4j
public class ImportCustomerInfoService extends SftpService implements IFFDataService<IFFCustomerInfoImportConfig> {

    @Autowired
    private GTBCustomerInfoTmpRepository gtbCustomerInfoTmpRepository;

    @Autowired
    private GTBCustomerInfoTmpBulkRepository gtbCustomerInfoTmpBulkRepository;

    @Autowired
    private DataTaskLogRepository dataTaskLogRepository;

    @Autowired
    private BatchService batchService;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private NativeSqlHelper nativeSqlHelper;

    @Override
    public void run(DataTask task, DataTaskLog taskLog) {
        log.info("----------- start execute task: {} -----------", task.getConfig());
        IFFCustomerInfoImportConfig config = JsonHelper.toObject(task.getConfig(), IFFCustomerInfoImportConfig.class);
        ChannelSftp channelSftp = getSFtpConnection(config);
        try {
            for (CustomerInfoFileType type : config.getImportFileType()) {
                switch (type) {
                    case BDP_CBS -> processFile(channelSftp, config, CustomerInfoFileType.BDP_CBS);
                    case AM_USR -> processFile(channelSftp, config, CustomerInfoFileType.AM_USR);
                    case BDP_COMPANY -> processFile(channelSftp, config, CustomerInfoFileType.BDP_COMPANY);
                    case BDP_PROFILE -> processFile(channelSftp, config, CustomerInfoFileType.BDP_PROFILE);
                    case DW56CIF -> processFile(channelSftp, config, CustomerInfoFileType.DW56CIF);
                }
            }
            syncTmpDataToCustomerTable(config);

            batchService.deleteCustomerInfoTmpData(config.getDataTmpStorageDays(), config.getDataBatchDeleteLimit());

            log.info("---------- execute task end: {} ----------", task.getTaskType());
        } catch (Exception e) {
            log.error("ImportCustomerInfoService failed: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            if (channelSftp != null) {
                channelSftp.disconnect();
            }
        }
        log.info("ImportCustomerInfoService finished.");
    }

    /**
     * 通过SFTP流式读取远程大文件，按指定长度分割为“行”处理，避免写入临时文件。
     * 性能和安全注意事项：
     * 1. 只要InputStream来源可靠（SFTP），不会有安全问题。
     * 2. 只分配一块小buffer，内存占用低，且不会落地磁盘，性能优于写临时文件。
     * 3. 若单条记录极大，buffer可适当调优。
     * 4. 若网络不稳定，SFTP流可能中断，需做好异常处理。
     */
    private void processFile(ChannelSftp channelSftp, IFFCustomerInfoImportConfig config, CustomerInfoFileType fileType) {
        String fileName = getFileNameByType(config, fileType);
        String dirPath = config.getRemoteDir();
        if (!dirPath.endsWith("/")) {
            dirPath += "/";
        }
        if (fileName == null || fileName.isEmpty()) {
            log.warn("{} file name is blank, skip.", fileType.name());
            return;
        }
        int totalLine = 0;
        int batchSize = config.getImportLineBatchSize();
        long totalFileSize = 0;
        long processedBytes = 0;
        long startTime = System.currentTimeMillis();
        Map<String, String[]> rawMap = new HashMap<>();
        List<GTBCustomerInfoTmp> dataList = new ArrayList<>();
        String remoteFilePath = dirPath + fileName;
        try (InputStream inputStream = channelSftp.get(remoteFilePath)) {
            // 按照指定长度读取每一行，不落地临时文件，直接流式处理
            int recordLength;
            java.nio.charset.Charset charset;
            switch (fileType) {
                case BDP_CBS:
                    recordLength = config.getBdpRecordLength();
                    charset = determineCharset(config.getFileBdpCbsCharset());
                    break;
                case AM_USR:
                    recordLength = config.getAmusrRecordLength();
                    charset = determineCharset(config.getFileAmUsrCharset());
                    break;
                case BDP_COMPANY:
                    recordLength = config.getCompRecordLength();
                    charset = determineCharset(config.getFileBdpCompanyCharset());
                    break;
                case BDP_PROFILE:
                    recordLength = config.getProfileRecordLength();
                    charset = determineCharset(config.getFileBdpProfileCharset());
                    break;
                case DW56CIF:
                    recordLength = config.getDwcifRecordLength();
                    charset = determineCharset(config.getFileDwCifCharset());
                    break;
                default:
                    log.warn("Unknown fileType: {}", fileType);
                    return;
            }

            // 直接通过SFTP获取远程文件输入流
            try {
                SftpATTRS attrs = channelSftp.stat(remoteFilePath);
                totalFileSize = attrs.getSize();
                log.info("文件信息: {}, 大小: {}, 记录长度: {}, 预计记录数: {}",
                        fileName,
                        formatFileSize(totalFileSize),
                        recordLength,
                        totalFileSize / recordLength);
            } catch (SftpException e) {
                log.warn("无法获取文件大小，进度显示将受限", e);
            }

            byte[] buffer = new byte[recordLength];
            int lineNum = 0;
            int bytesRead;
            while ((bytesRead = readFully(inputStream, buffer, recordLength)) != -1) {
                lineNum++;
                // 检查是否读取了完整的记录
                if (bytesRead < recordLength) {
                    log.warn("不完整的记录: 期望 {} 字节, 实际读取 {} 字节",
                            recordLength, bytesRead);
                    continue;
                }

                // 解码并处理记录
                String line = new String(buffer, charset);

//                String line = new String(buffer, charset);
                if (StringUtils.isEmpty(line) || !line.startsWith(config.getDataPrefix())) {
                    // 跳过首行
                    continue;
                }
                if (!isValidLine(line, config, fileType)) {
                    continue;
                }
                String[] parsed = parseLine(line, config, fileType);
                if (parsed == null) {
                    continue;
                }
                String uniqueId = getUniqueId(parsed, fileType);
                rawMap.put(uniqueId, parsed);
                processedBytes += bytesRead;

                if (rawMap.size() >= batchSize) {
                    generateUpsertLine(config, rawMap, dataList, fileType);
                    gtbCustomerInfoTmpBulkRepository.bulkUpsert(dataList, fileType);
                    totalLine += dataList.size();
                    log.info("batch save {} data: {}", fileType.name(), dataList.size());
                    printProgress(processedBytes, totalFileSize, totalLine, startTime);
                    rawMap.clear();
                    dataList.clear();
                }
            }
            // 处理剩余数据
            if (!rawMap.isEmpty()) {
                generateUpsertLine(config, rawMap, dataList, fileType);
                gtbCustomerInfoTmpBulkRepository.bulkUpsert(dataList, fileType);
                totalLine += dataList.size();
                log.info("batch save {} data: {}", fileType.name(), dataList.size());
            }
            printFinalProgress(processedBytes, totalFileSize, totalLine, totalLine, startTime);
            log.info("Processed {} file :{} SUCCESS, TOTAL LINE: {}", fileType.name(), fileName, totalLine);

        } catch (Exception ex) {
            log.error("文件 {} 导入失败", fileName, ex);
        }
    }


    /**
     * 解析每行数据
     */
    private String[] parseLine(String line, IFFCustomerInfoImportConfig config, CustomerInfoFileType fileType) {
        try {
            switch (fileType) {
                case BDP_CBS: {
                    String ac68 = line.substring(config.getBdpAcNoLeftIndex(), config.getBdpAcNoRightIndex()).trim();
                    String cin = line.substring(config.getBdpCinNoLeftIndex(), config.getBdpCinNoRightIndex()).trim();
                    String bankCode = line.substring(config.getBdpBankCodeLeftIndex(), config.getBdpBankCodeRightIndex());
                    if (ac68.isEmpty() && cin.isEmpty()) return null;
                    return new String[]{ac68, cin, bankCode, ac68 + "_" + cin};
                }
                case AM_USR: {
                    String ac68 = line.substring(config.getAmusrAcNoLeftIndex(), config.getAmusrAcNoRightIndex()).trim();
                    String usrId = line.substring(config.getAmusrUsrIdLeftIndex(), config.getAmusrUsrIdRightIndex()).trim();
                    String statPrimary = line.substring(config.getAmusrStatPrimaryLeftIndex(), config.getAmusrStatPrimaryRightIndex()).trim();
                    if (ac68.isEmpty()) return null;
                    return new String[]{ac68, usrId, statPrimary};
                }
                case BDP_COMPANY: {
                    String ac68 = line.substring(config.getCompAcNoLeftIndex(), config.getCompAcNoRightIndex()).trim();
                    String cinNo = line.substring(config.getCompCinNoLeftIndex(), config.getCompCinNoRightIndex()).trim();
                    String segmentCode = line.substring(config.getCompSegmenCodeLeftIndex(), config.getCompSegmenCodeRightIndex()).trim();
                    if (ac68.isEmpty() || cinNo.isEmpty()) return null;
                    return new String[]{ac68, cinNo, segmentCode, ac68 + "_" + cinNo};
                }
                case BDP_PROFILE: {
                    String ac68 = line.substring(config.getCompAcNoLeftIndex(), config.getCompAcNoRightIndex()).trim();
                    String userId = line.substring(config.getProfileUserIdLeftIndex(), config.getProfileUserIdRightIndex()).trim();
                    String moduleId = line.substring(config.getProfileModuleIdLeftIndex(), config.getProfileModuleIdRightIndex());
                    if (ac68.isEmpty()) return null;
                    return new String[]{ac68, userId, moduleId};
                }
                case DW56CIF: {
                    String cinNo = line.substring(config.getDwcifCinNoLeftIndex(), config.getDwcifCinNoRightIndex()).trim();
                    String managerId = line.substring(config.getDwcifCustomerManagerIdLeftIndex(), config.getDwcifCustomerManagerIdRightIndex()).trim();
                    if (cinNo.isEmpty()) return null;
                    return new String[]{cinNo, managerId};
                }
                default:
                    return null;
            }
        } catch (Exception e) {
            log.warn("Parse line error: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 批量 upsert 数据
     */
    private void generateUpsertLine(IFFCustomerInfoImportConfig config, Map<String, String[]> rawMap, List<GTBCustomerInfoTmp> dataList, CustomerInfoFileType fileType) {
        if (rawMap == null || rawMap.isEmpty()) {
            return;
        }
        Date importDate = getImportDate(config);
        Map<String, GTBCustomerInfoTmp> existedCustomerDataMap = new HashMap<>();
        List<GTBCustomerInfoTmp> customerInfoTmpList = null;
//        switch (fileType) {
//            case BDP_CBS:
//            case BDP_COMPANY:
//                customerInfoTmpList = gtbCustomerInfoTmpRepository
//                        .findAllByImportDateAndCbsAcCinNoIn(importDate, rawMap.keySet());
//                existedCustomerDataMap = customerInfoTmpList.stream().collect(
//                        Collectors.toMap(GTBCustomerInfoTmp::getCbsAcCinNo, Function.identity(), (existing, replacement) -> existing));
//                break;
//            case DW56CIF:
//                customerInfoTmpList = gtbCustomerInfoTmpRepository
//                        .findAllByImportDateAndCinNoIn(importDate, rawMap.keySet());
//                existedCustomerDataMap = customerInfoTmpList.stream().collect(
//                        Collectors.toMap(GTBCustomerInfoTmp::getCinNo, Function.identity(), (existing, replacement) -> existing));
//                break;
//            case AM_USR:
//            case BDP_PROFILE:
//                customerInfoTmpList = gtbCustomerInfoTmpRepository
//                        .findAllByImportDateAndCbsACIn(importDate, rawMap.keySet());
//                existedCustomerDataMap = customerInfoTmpList.stream().collect(
//                        Collectors.toMap(GTBCustomerInfoTmp::getCbsAC, Function.identity(), (existing, replacement) -> existing));
//                break;
//            default:
//                log.error("error file type");
//        }

        for (Map.Entry<String, String[]> entry : rawMap.entrySet()) {
            GTBCustomerInfoTmp customerData = existedCustomerDataMap.getOrDefault(entry.getKey(), new GTBCustomerInfoTmp());
            customerData.setImportDate(importDate);
            switch (fileType) {
                case BDP_CBS:
                    customerData.setCbsAC(entry.getValue()[0]);
                    customerData.setCinNo(entry.getValue()[1]);
                    customerData.setCbsAcCinNo(entry.getValue()[0] + "_" + entry.getValue()[1]);
                    customerData.setBankCode(entry.getValue()[2]);
                    break;
                case AM_USR:
                    customerData.setCbsAC(entry.getValue()[0]);
                    customerData.setUserId(entry.getValue()[1]);
                    customerData.setStatPrimary(entry.getValue()[2]);
                    break;
                case BDP_COMPANY:
                    customerData.setCbsAC(entry.getValue()[0]);
                    customerData.setCinNo(entry.getValue()[1]);
                    customerData.setSegmentCode(entry.getValue()[2]);
                    customerData.setCbsAcCinNo(entry.getValue()[0] + "_" + entry.getValue()[1]);
                    break;
                case BDP_PROFILE:
                    customerData.setCbsAC(entry.getValue()[0]);
                    customerData.setUserId(entry.getValue()[1]);
                    customerData.setModuleId(entry.getValue()[2]);
                    break;
                case DW56CIF:
                    customerData.setCinNo(entry.getValue()[0]);
                    customerData.setCustomerManagerId(entry.getValue()[1]);
                    break;
                default:
                    break;
            }
            dataList.add(customerData);
        }
    }

    /**
     * 从输入流中读取指定长度的字节，直到填满buffer或到达流末尾
     * @return 实际读取的字节数，若到达流末尾且未读到任何字节则返回-1
     */
    private int readFully(InputStream inputStream, byte[] buffer, int length) throws IOException {
        int totalBytesRead = 0;
        int bytesRead;

        while (totalBytesRead < length) {
            bytesRead = inputStream.read(buffer, totalBytesRead, length - totalBytesRead);
            if (bytesRead == -1) {
                // 遇到文件结尾
                return -1;
            }
            totalBytesRead += bytesRead;
        }

        return totalBytesRead;
    }

    /**
     * 根据文件类型获取文件名
     */
    private String getFileNameByType(IFFCustomerInfoImportConfig config, CustomerInfoFileType fileType) {
        switch (fileType) {
            case BDP_CBS:
                return config.getFileBdpCbsPrefix();
            case AM_USR:
                return config.getFileAmUsrPrefix();
            case BDP_COMPANY:
                return config.getFileBdpCompanyPrefix();
            case BDP_PROFILE:
                return config.getFileBdpProfilePrefix();
            case DW56CIF:
                return config.getFileDwCifPrefix();
            default:
                return "";
        }
    }

    /**
     * 校验每行数据是否合法
     */
    private boolean isValidLine(String line, IFFCustomerInfoImportConfig config, CustomerInfoFileType fileType) {
        switch (fileType) {
            case BDP_CBS:
                return line.length() >= config.getBdpRecordLength();
            case AM_USR:
                return line.length() >= config.getAmusrRecordLength();
            case BDP_COMPANY:
                return line.length() >= config.getCompRecordLength();
            case BDP_PROFILE:
                return line.length() >= config.getProfileRecordLength();
            case DW56CIF:
                return line.length() >= config.getDwcifRecordLength();
            default:
                return false;
        }
    }

    /**
     * 获取唯一标识
     */
    private String getUniqueId(String[] parsed, CustomerInfoFileType fileType) {
        switch (fileType) {
            case BDP_CBS:
            case BDP_COMPANY:
                return parsed.length > 3 ? parsed[3] : "";
            case AM_USR:
            case BDP_PROFILE:
            case DW56CIF:
                return parsed[0];
            default:
                return "";
        }
    }

    @Override
    public void saveFileDataToDB(IFFCustomerInfoImportConfig config, Path tmpFile) {
        // 可扩展：实现通用文件导入
    }

    @Override
    public void updateDataTaskLogStatus(DataTaskLog dataTaskLog, DataTaskLogStatus status) {
        dataTaskLog.setStatus(status);
        dataTaskLogRepository.save(dataTaskLog);
    }

    @Override
    public void deleteFile(Path filePath) {
        try {
            if (filePath != null && Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("删除临时文件 {} 成功", filePath.getFileName());
            }
        } catch (Exception ex) {
            log.error("删除临时文件 {} 失败，{}", filePath != null ? filePath.getFileName() : "null", ex.getMessage());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void syncTmpDataToCustomerTable(IFFCustomerInfoImportConfig config) {
        int pageSize = 10000;
        long lastId = 0L;
        List<String> fieldLabels = List.of("bankCode","segmentCode","moduleId","customerManagerId","statPrimary","userId");

        Date importDate = getImportDate(config);
        List<GTBCustomerInfoTmp> pageList;
        // 获取自定义字段配置
        OrganizationConfig orgConfig = findCustomerConfig();
        OrgConfigExtendCustomerFieldDto extendCustomerFieldDto = JsonHelper.toObject(orgConfig.getConfig(), OrgConfigExtendCustomerFieldDto.class);
        Map<String, String> fieldMap = new HashMap<>();
        Map<String, Map<String, String>> fieldOptionMap = new HashMap<>();
        extendCustomerFieldDto.getExtendFields().forEach(f -> {
            String label = f.getLabel();
            if (fieldLabels.contains(label)) {
                fieldMap.put(label, f.getProp());
                if (f.getType() == ExtendCustomerFieldType.SINGLE_CHOICE) {
                    Map<String, String> optionMap = f.getOptions().stream().collect(Collectors.toMap(FieldOption::getLabel, FieldOption::getValue));
                    fieldOptionMap.put(f.getProp(), optionMap);
                }
            }
        });
        
        do {
            // 分页查询当天导入且acNo、cinNo、userId不为空的数据
            pageList = gtbCustomerInfoTmpRepository
                    .findAllByImportDateAndIdGreaterThan(importDate, lastId, PageRequest.of(0, pageSize));
            if (pageList == null || pageList.isEmpty()) {
                break;
            }
            // 过滤acNo、cinNo、userId都不为空的数据
            List<GTBCustomerInfoTmp> validList = pageList.stream()
                    .filter(d -> d.getCbsAC() != null && !d.getCbsAC().isEmpty()
                            && d.getCinNo() != null && !d.getCinNo().isEmpty()
                            && d.getUserId() != null && !d.getUserId().isEmpty())
                    .collect(Collectors.toList());
            if (validList.isEmpty()) {
                lastId = pageList.get(pageList.size() - 1).getId();
                continue;
            }
            // 构造acNo_cinNo_userId集合
            Set<String> keySet = validList.stream()
                    .map(d -> d.getCbsAC() + "_" + d.getCinNo() + "_" + d.getUserId())
                    .collect(Collectors.toSet());

            List<Customer> customers = findAllCustomerByExternalUserId(keySet);
            Map<String, Customer> customerMap = customers.stream()
                    .collect(Collectors.toMap(
                            Customer::getExternalUserId,
                            Function.identity()
                    ));

            List<Customer> toUpdate = new ArrayList<>();
            for (GTBCustomerInfoTmp tmp : validList) {
                String key = tmp.getCbsAC() + "_" + tmp.getCinNo() + "_" + tmp.getUserId();
                Customer customer = customerMap.get(key);
                // 组装extendFields
                Map<String, Object> extendMap = new HashMap<>();
                if (tmp.getBankCode() != null && fieldMap.get("bankCode") != null) {
                    extendMap.put(Objects.toString(fieldMap.get("bankCode")), tmp.getBankCode());
                }
                if (tmp.getSegmentCode() != null && fieldMap.get("segmentCode") != null) {
                    extendMap.put(Objects.toString(fieldMap.get("segmentCode")), tmp.getSegmentCode());
                }
                if (tmp.getModuleId() != null && fieldMap.get("moduleId") != null) {
                    extendMap.put(Objects.toString(fieldMap.get("moduleId")), tmp.getModuleId());
                }
//                if (tmp.getCustomerManagerId() != null && fieldMap.get("customerManagerId") != null) {
//                    extendMap.put(Objects.toString(fieldMap.get("customerManagerId")), tmp.getCustomerManagerId());
//                }
                if (tmp.getStatPrimary() != null && fieldMap.get("statPrimary") != null) {
                    String statPrimary = "Y".equals(tmp.getStatPrimary()) ? "PU" : (
                            "N".equals(tmp.getStatPrimary()) ? "DU" : "CU");
                    if (fieldOptionMap.get("statPrimary") != null) {
                        extendMap.put(Objects.toString(fieldMap.get("statPrimary")), fieldOptionMap.get("statPrimary").get(statPrimary));
                    }
                    extendMap.put(Objects.toString(fieldMap.get("statPrimary")), statPrimary);
                }
                if (tmp.getUserId() != null && fieldMap.get("userId") != null) {
                    extendMap.put(Objects.toString(fieldMap.get("userId")), tmp.getUserId());
                }
                if (customer != null) {
                    customer.setExtendFields(extendMap);
                    toUpdate.add(customer);
                } else {
                    customer = new Customer();
                    customer.setExternalUserId(key);
                    customer.setExtendFields(extendMap);
                    toUpdate.add(customer);
                }
            }
            if (!toUpdate.isEmpty()) {
                customerRepository.saveAll(toUpdate);
            }
            lastId = pageList.get(pageList.size() - 1).getId();
        } while (pageList.size() == pageSize);
    }

    private List<Customer> findAllCustomerByExternalUserId(Set<String> euids) {
        String sql = String
                .format("select id,external_user_id from customer where external_user_id in (%s)",
                        StringUtils.join(euids, ","));
        log.info("find response sql: {}", sql);
        return nativeSqlHelper.queryListObject(sql, Customer.class);
    }

    private OrganizationConfig findCustomerConfig() {
        String sql = "select config from organization_config where type = 'extendCustomerField' limit 1";
        return nativeSqlHelper.queryObject(sql, OrganizationConfig.class);
    }
}
