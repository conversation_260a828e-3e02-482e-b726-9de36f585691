package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.consts.DataTXCodeType;
import cn.hanyi.compat.bochk.task.consts.DataTaskLogStatus;
import cn.hanyi.compat.bochk.task.dto.GTBTmpDataCountDto;
import cn.hanyi.compat.bochk.task.dto.IFFCountDataImportConfig;
import cn.hanyi.compat.bochk.task.entity.DataTask;
import cn.hanyi.compat.bochk.task.entity.DataTaskLog;
import cn.hanyi.compat.bochk.task.entity.GTBCountDataTmp;
import cn.hanyi.compat.bochk.task.repository.DataTaskLogRepository;
import cn.hanyi.compat.bochk.task.repository.GTBCountDataTmpRepository;
import cn.hanyi.compat.bochk.task.service.impls.IFFDataService;
import cn.hanyi.survey.core.entity.GTBCountData;
import cn.hanyi.survey.core.repository.GTBCountDataRepository;
import com.jcraft.jsch.ChannelSftp;
import java.io.FileInputStream;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

@Service
@EnableAsync
@Slf4j
public class ImportIFFDataServiceImpl extends SftpService implements IFFDataService<IFFCountDataImportConfig> {

    @Autowired
    private DataTaskLogRepository dataTaskLogRepository;

    @Autowired
    private SftpService sftpService;

    @Autowired
    private GTBCountDataRepository gtbCountDataRepository;

    @Autowired
    private GTBCountDataTmpRepository gtbCountDataTmpRepository;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private BatchService batchService;

    @Override
    public void run(DataTask task, DataTaskLog taskLog) {
        log.info("---------- start execute task: {} ----------", task.getConfig());
        IFFCountDataImportConfig config = JsonHelper.toObject(task.getConfig(), IFFCountDataImportConfig.class);
        Path tempFile = null;
        try {
            ChannelSftp channelSftp = sftpService.getSFtpConnection(config);
            tempFile = sftpService.downloadFile(channelSftp, config.getRemoteDir(), config.getFileLocalPath(), config.getFileName());
        } catch (Exception ex) {
            log.error("download file from sftp error, caused by : {}", ex.getMessage());
            ex.printStackTrace();
            throw new RuntimeException();
        }
        if (tempFile != null) {
            saveFileDataToDB(config, tempFile);
            syncTmpDataToActualTable(config);
            try {
                deleteFile(tempFile);
                log.info("删除临时文件 {} 成功", tempFile.getFileName());
            } catch (Exception ex) {
                log.error("删除临时文件 {} 失败，{}", tempFile.getFileName(), ex.getMessage());
            }
            batchService.deleteOldIFFCountTmpData(config.getDataTmpStorageDays(), config.getDataBatchDeleteLimit());
        }
        log.info("---------- execute task end: {} ----------", task.getTaskType());
    }

    @Override
    public void updateDataTaskLogStatus(DataTaskLog dataTaskLog, DataTaskLogStatus status) {
        dataTaskLog.setStatus(status);
        dataTaskLogRepository.save(dataTaskLog);
    }

    @Override
    public void saveFileDataToDB(IFFCountDataImportConfig config, Path tempFile) {
        if (tempFile == null || !Files.exists(tempFile)) {
            throw new IllegalArgumentException("Invalid file path");
        }
        int batchSize = config.getImportLineBatchSize();
        List<GTBCountDataTmp> tmpList = new ArrayList<>(batchSize);

        Charset charset = determineCharset(config.getFileCharset());
        log.info("start save file to db, {}", charset);
        int totalLine = 0;
        try (FileInputStream fis = new FileInputStream(tempFile.toFile());
                FileChannel fileChannel = fis.getChannel()) {
            ByteBuffer buffer = ByteBuffer.allocate(config.getRecordLength());
            long fileSize = fileChannel.size();
            long position = 0;

            while (position < fileSize) {
                fileChannel.position(position);
                buffer.clear();
                int bytesRead = fileChannel.read(buffer);

                if (bytesRead == -1) {
                    break; // 文件读取完毕
                }

                buffer.flip();
                String record = charset.decode(buffer).toString();
                // 如果记录以 "1" 开头，则处理
                if (record.startsWith(config.getDataPrefix())) {
                    processGTBCountLine(config, record, tmpList);

                    // 批量插入
                    if (tmpList.size() >= batchSize) {
//                        batchService.batchInsertGTBCountDataTmp(tmpList, batchSize);
                        gtbCountDataTmpRepository.saveAll(tmpList);
                        totalLine += tmpList.size();
                        tmpList.clear();
                    }
                }
                position += config.getRecordLength(); // 移动到下一条记录
            }
            // 处理剩余的数据
            if (!tmpList.isEmpty()) {
//                batchService.batchInsertGTBCountDataTmp(tmpList, batchSize);
                gtbCountDataTmpRepository.saveAll(tmpList);
                totalLine+=tmpList.size();
            }
            log.info("successed save file to db, total line {}", totalLine);
        } catch (Exception e) {
            log.error("Failed to process file");
            e.printStackTrace();
            throw new RuntimeException("Failed to process file", e);
        }
    }

    private void processGTBCountLine(IFFCountDataImportConfig config, String line, List<GTBCountDataTmp> tmpList) {
        if (line == null || !line.startsWith(config.getDataPrefix())) {
            // 跳过无效行
            return;
        }
        log.debug("start process gtbcount line");
        String txCode = line.substring(config.getTxCodeLeftIndex(), config.getTxCodeRightIndex()).trim();
        String cbsAccount = line.substring(config.getEuidLeftIndex(), config.getEuidRightIndex()).trim();
        if (DataTXCodeType.contains(txCode)) {
            GTBCountDataTmp tmp = new GTBCountDataTmp();
            tmp.setCbsAccount(cbsAccount);
            tmp.setTxCodeType(DataTXCodeType.valueOf(txCode));
            tmp.setImportDate(getImportDate(config));
            tmpList.add(tmp);
        }
    }

    private void syncTmpDataToActualTable(IFFCountDataImportConfig config) {
        log.info("start sync data to actual table");
        List<Map<String, Object>> res = gtbCountDataTmpRepository.countByImportDate(getImportDate(config));
        List<GTBTmpDataCountDto> tmpDataCountDtos = res.stream().map(r -> {
            GTBTmpDataCountDto countDto = new GTBTmpDataCountDto();
            countDto.setCbsAccount(String.valueOf(r.get("cbsAccount")));
            countDto.setTxCodeType(DataTXCodeType.fromOrdinal(Integer.parseInt(String.valueOf(r.get("txCodeType")))));
            countDto.setCount(Integer.valueOf(String.valueOf(r.get("countRes"))));
            return countDto;
        }).collect(Collectors.toList());
        Set<String> accounts = tmpDataCountDtos.stream().map(GTBTmpDataCountDto::getCbsAccount).collect(Collectors.toSet());
        List<GTBCountData> dataCounts = gtbCountDataRepository.findAllByCbsAccountIn(accounts);
        Map<String, GTBCountData> dataCountMap = dataCounts.stream()
                .collect(Collectors.toMap(GTBCountData::getCbsAccount, Function.identity()));
        log.info("GTBAccounts count: {}", accounts.size());
        Date importDate = getImportDate(config);
        tmpDataCountDtos.forEach(d -> {
            try {
                GTBCountData updatedCount = dataCountMap.get(d.getCbsAccount());
                if (updatedCount == null) {
                    updatedCount = new GTBCountData();
                    updatedCount.setCbsAccount(d.getCbsAccount());
                    dataCountMap.put(d.getCbsAccount(), updatedCount);
                }
                if (updatedCount.getImportDate() == null || config.getEnableIncrement() ||
                        !formatImportDate(config, updatedCount.getImportDate()).equals(formatImportDate(config, importDate))) {
                    updateCount(config, updatedCount, d);
                }
                updatedCount.setImportDate(importDate);
            } catch (Exception ex) {
                log.error("临时表数据统计失败,account：{},caused by: {}",d.getCbsAccount(), ex.getMessage());
                ex.printStackTrace();
            }
        });
        transactionTemplate.execute(status -> {
            gtbCountDataRepository.saveAll(dataCountMap.values());
            return null;
        });
        log.info("successed sync data to actual table");
    }

    private void updateCount(IFFCountDataImportConfig config, GTBCountData gtbCountData,GTBTmpDataCountDto tmpDataCountDto) {
        Integer count = tmpDataCountDto.getCount();
        switch (tmpDataCountDto.getTxCodeType()) {
            case Z100 -> {
                gtbCountData.setGIBLoginCounts((gtbCountData.getGIBLoginCounts() == null ? 0 : gtbCountData.getGIBLoginCounts()) + count);
                gtbCountData.setTotalLoginCounts((gtbCountData.getTotalLoginCounts() == null ? 0 : gtbCountData.getTotalLoginCounts()) + count);
            }
            case Z103 -> {
                gtbCountData.setGMBLoginCounts((gtbCountData.getGMBLoginCounts() == null ? 0 : gtbCountData.getGMBLoginCounts()) + count);
                gtbCountData.setTotalLoginCounts((gtbCountData.getTotalLoginCounts() == null ? 0 : gtbCountData.getTotalLoginCounts()) + count);
            }
            case P100 -> {
                gtbCountData.setTransferCounts((gtbCountData.getTransferCounts() == null ? 0 : gtbCountData.getTransferCounts()) + count);
                gtbCountData.setTransactionCounts((gtbCountData.getTransactionCounts() == null ? 0 : gtbCountData.getTransactionCounts()) + count);
            }
            case P140 -> {
                gtbCountData.setWireCounts((gtbCountData.getWireCounts() == null ? 0 : gtbCountData.getWireCounts()) + count);
                gtbCountData.setTransactionCounts((gtbCountData.getTransactionCounts() == null ? 0 : gtbCountData.getTransactionCounts()) + count);
            }
            case P160 -> {
                gtbCountData.setRemittanceCounts((gtbCountData.getRemittanceCounts() == null ? 0 : gtbCountData.getRemittanceCounts()) + count);
                gtbCountData.setTransactionCounts((gtbCountData.getTransactionCounts() == null ? 0 : gtbCountData.getTransactionCounts()) + count);
            }
            case P280 -> {
                gtbCountData.setFpsCounts((gtbCountData.getFpsCounts() == null ? 0 : gtbCountData.getFpsCounts()) + count);
                gtbCountData.setTransactionCounts((gtbCountData.getTransactionCounts() == null ? 0 : gtbCountData.getTransactionCounts()) + count);
            }
            case T180 -> gtbCountData.setSmeCounts((gtbCountData.getSmeCounts() == null ? 0 : gtbCountData.getSmeCounts()) + count);
            case T110 -> gtbCountData.setRegularCounts((gtbCountData.getRegularCounts() == null ? 0 : gtbCountData.getRegularCounts()) + count);
            case P262 -> gtbCountData.setDepositCounts((gtbCountData.getDepositCounts() == null ? 0 : gtbCountData.getDepositCounts()) + count);
            case Z170 -> gtbCountData.setEverCounts((gtbCountData.getEverCounts() == null ? 0 : gtbCountData.getEverCounts()) + count);
            case Z130 -> gtbCountData.setEconCounts((gtbCountData.getEconCounts() == null ? 0 : gtbCountData.getEconCounts()) + count);
            default -> log.warn("unknowm tx_code");
        }
    }

//    @Override
    public void deleteOldTmpData(IFFCountDataImportConfig config) {
        try {
            transactionTemplate.execute(status  -> {
                gtbCountDataTmpRepository.deleteAllByImportDateBefore(
                    Date.from(LocalDate.now().minusDays(config.getDataTmpStorageDays())
                            .atStartOfDay(ZoneId.systemDefault()).toInstant()), config.getDataBatchDeleteLimit());
                return null;
            });

            log.info("successed deleted old data , {}", LocalDate.now().minusDays(config.getDataTmpStorageDays())
                    .atStartOfDay(ZoneId.systemDefault()));
        } catch (Exception ex) {
            log.warn("delete old data error, caused by {}", ex.getMessage());
            ex.printStackTrace();
        }
    }

    private String formatImportDate(IFFCountDataImportConfig config, Date date) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(config.getImportDateFormatter());
        return dateFormat.format(date);
    }
}
