package cn.hanyi.compat.bochk.task.entity;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;

@Entity
@Table(name = "survey_send_iff_record")
@Getter
@Setter
public class SurveySendIFFRecord extends BaseEntity {
    @Column(name = "euid")
    @Schema(description = "客户编号")
    private String externalUserId;

    @Column(name = "send_time")
    @Schema(description = "发送时间")
    private Date sendTime;

    @Column(name = "survey_url")
    @Schema(description = "问卷链接")
    private String surveyUrl;

    @Column(name = "msg_subject")
    @JsonAlias(value = "msg_subject")
    @Schema(description = "客户编号")
    private String msgSubject;

    @Column(name = "msg_content")
    @JsonAlias(value = "msg_content")
    @Schema(description = "发送内容")
    private String msgContent;

    @Column(name = "msg_button")
    @JsonAlias(value = "msg_button")
    @Schema(description = "按钮文案")
    private String msgButton;

    @Column(name = "send_record_id")
    @Schema(description = "发送记录id")
    private Long sendRecordId;


}
