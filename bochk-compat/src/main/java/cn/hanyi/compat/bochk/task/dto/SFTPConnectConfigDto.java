package cn.hanyi.compat.bochk.task.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SFTPConnectConfigDto {

    private String sftpHost;
    private Integer sftpPort;
    private String sftpUser = "root";
    private String sftpPassword = "";
    private long retryInterval = 10000;
    private String privateKeyPath = "/tmp/files/";

    private int sessionTimeOut = 30000;
    private int channelTimeOut = 15000;
    private int readTimeOut = 600000;
}
