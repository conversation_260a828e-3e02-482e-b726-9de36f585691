package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.consts.DataTaskLogStatus;
import cn.hanyi.compat.bochk.task.dto.DataTaskExecuteDto;
import cn.hanyi.compat.bochk.task.dto.SurveySendIFFRecordExportConfig;
import cn.hanyi.compat.bochk.task.entity.DataTaskLog;
import cn.hanyi.compat.bochk.task.entity.SurveySendIFFRecord;
import cn.hanyi.compat.bochk.task.repository.DataTaskLogRepository;
import cn.hanyi.compat.bochk.task.repository.SurveySendIFFRepository;
import cn.hanyi.compat.bochk.task.service.impls.IFFExportDataService;
import java.io.BufferedWriter;
import java.io.IOException;
import java.net.http.HttpResponse;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.shade.io.netty.handler.codec.http.HttpResponseStatus;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ExportSurveySendIFFRecordService extends SftpService implements
        IFFExportDataService<SurveySendIFFRecordExportConfig> {

    @Autowired
    DataTaskLogRepository dataTaskLogRepository;

    @Autowired
    SurveySendIFFRepository surveySendIFFRepository;

    @Autowired
    private NotifyService notifyService;

    @Override
    public void run(DataTaskExecuteDto dto, DataTaskLog taskLog) {
        log.info("----------- start execute task: {} -----------", dto.getTask().getConfig());
        try {
            SurveySendIFFRecordExportConfig config = JsonHelper
                    .toObject(dto.getTask().getConfig(), SurveySendIFFRecordExportConfig.class);
            LocalDate exportDate = LocalDate.now().minusDays(config.getMinusDays());
            LocalDate endDate = dto.getEndDate();
            if (dto.getTriggerDate() != null) {
                exportDate = dto.getTriggerDate();
            }
            if (endDate == null) {
                endDate = exportDate.plusDays(config.getPlusDays());
            }
            Path tempFile = exportData(config, exportDate, endDate);
            if (!dto.getDoNotUpload()) {
                uploadFile(tempFile, config);
            }
            if (!dto.getDoNotNotify()) {
                HttpResponse<String> response = notifyService.sendNotify(config,exportDate);
                if (response.statusCode() == HttpResponseStatus.OK.code()) {
                    taskLog.setStatus(DataTaskLogStatus.SUCCESSED);
                } else {
                    taskLog.setStatus(DataTaskLogStatus.FAILED);
                }
                taskLog.setFailMsg(String.valueOf(response.statusCode()));
                dataTaskLogRepository.save(taskLog);
            }
        } catch (Exception e) {
            // 异常日志记录
            log.error("execute task error: caused by: {}", e.getMessage());
            e.printStackTrace();
            taskLog.setStatus(DataTaskLogStatus.FAILED);
            taskLog.setFailMsg(e.getMessage());
            dataTaskLogRepository.save(taskLog);
        }
        log.info("---------- execute task end: {} ----------", dto.getTask().getTaskType());

    }

    @Override
    public Long writeTableData(SurveySendIFFRecordExportConfig config, BufferedWriter writer, LocalDate triggerDate, LocalDate endDate) throws IOException {

        return exportPaginatedData(config, writer, triggerDate, (page,date) -> findAllSendIFFRecord(PageRequest.of(page, config.getPageLimit()), date, endDate));
    }

    @Override
    public String buildRowData(SurveySendIFFRecordExportConfig config, Object dto) {
        if (dto instanceof SurveySendIFFRecord) {
            return buildSurveySendIFFRecordRowData(config, (SurveySendIFFRecord) dto);
        }
        throw new IllegalArgumentException("Unsupported DTO type");
    }

    private String buildSurveySendIFFRecordRowData(SurveySendIFFRecordExportConfig config, SurveySendIFFRecord record) {
        SimpleDateFormat sdf = new SimpleDateFormat(config.getDataDateFormatter());
        StringBuilder row = new StringBuilder();
        row.append(config.getRowDataPrefix());
        row.append(formatString(Objects.toString(record.getExternalUserId(),null), config.getRecordEuidLentgh(), config.getRowFillChar(), config));
        row.append(formatString(record.getSendTime() == null ? null : sdf.format(record.getSendTime()), config.getRecordSendTimeLentgh(), config.getRowFillChar(), config));
        row.append(formatString(Objects.toString(record.getSurveyUrl(),null), config.getRecordSurveyUrlLength(), config.getRowFillChar(), config));
        row.append(formatString(Objects.toString(record.getMsgSubject(),null), config.getRecordMsgSubjectLength(), config.getRowFillChar(), config));
        row.append(formatString(Objects.toString(record.getMsgContent(),null), config.getRecordMsgContentLength(), config.getRowFillChar(), config));
        row.append(formatString(Objects.toString(record.getMsgButton(),null), config.getRecordMsgButtonLength(), config.getRowFillChar(), config));
        return formatString(row.toString(), config.getRowLength(), config.getRowFillChar(), config);
    }

    private List<SurveySendIFFRecord> findAllSendIFFRecord(Pageable page, LocalDate triggerDate, LocalDate endDate) {
        return surveySendIFFRepository.findAllByCreateTimeBetween(Date.from(triggerDate.atStartOfDay(
                ZoneId.systemDefault()).toInstant()), Date.from(endDate.atStartOfDay(
                        ZoneId.systemDefault()).toInstant()), page);
    }

    @Override
    public void updateDataTaskLogStatus(DataTaskLog dataTaskLog, DataTaskLogStatus status) {
        dataTaskLog.setStatus(status);
        dataTaskLogRepository.save(dataTaskLog);
    }
}
