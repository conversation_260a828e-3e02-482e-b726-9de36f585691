package cn.hanyi.compat.bochk.task.property;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "project-compat.bochk.gtb-blacklist")
@Getter
@Setter
public class GtbBlackListProperty {
    private Boolean blackListEnable = true;
    private String blackLisUserSplit = "_";
    private List<String> blackListStatus = List.of("101", "202", "102", "103", "203", "106", "105", "107", "002", "112", "206", "114", "115", "116", "117");
    private List<String> blackListBranchNo = List.of("88333");
    private List<String> blackListDeptCode = List.of("EXX", "H04");
    private String blackLisGuarInd = "Y";

}
