package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.dto.DataExportConfig;
import cn.hanyi.compat.bochk.task.dto.DataExportNotifyConfig;
import cn.hanyi.compat.bochk.task.property.DataTaskProperty;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import javax.annotation.PostConstruct;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class NotifyService {
    @Autowired
    private DataTaskProperty property;

    private static HttpClient client;

    @PostConstruct
    private void setUp() {
        try {
            client = HttpClient.newBuilder()
                    .sslContext(createSSLContext())
                    .build();
        } catch (Exception e) {
            log.error("Error initializing SSLContext: " + e.getMessage());
        }
    }

    public HttpResponse<String> sendNotify(DataExportConfig config, LocalDate exportDate) {
        DataExportNotifyConfig notifyConfig = config.getNotifyConfig();

        notifyConfig.getReqBody().setRefKey(exportDate.format(DateTimeFormatter.ofPattern(config.getAcDateFormatter())));
        notifyConfig.getReqBody().setAcDate(exportDate.format(DateTimeFormatter.ofPattern(config.getAcDateFormatter())));
        notifyConfig.getReqBody().setIffId(config.getIffId());
        notifyConfig.getReqBody().setDatasetName(config.getDatasetName());
        notifyConfig.getReqBody().setSrcApp(config.getSrcApp());
        notifyConfig.getReqHeader().setCallCode(config.getCallCode());
        notifyConfig.getReqHeader().setChannelCode(config.getChannelCode());

        try {
            String requestBody = JsonHelper.toJson(notifyConfig);
            log.info("notify request body: {}", requestBody);

            if (config.getEnableSSL()) {
                loadCertficate(StringUtils.isNotEmpty(property.getCacertPath()) ? property.getCacertPath() : config.getCaCertPath());
            } else {
                client = HttpClient.newBuilder()
                        .build();
            }

            // Create the POST request
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(StringUtils.isNotEmpty(property.getApiUrl()) ? property.getApiUrl() : config.getApiUrl()))
                    .header("x-Gateway-APIKey", property.getApiKey())
                    .header("Content-Type", "application/json")
                    .header("Accept", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();
            log.info("notify request headers: {}", request.headers());

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("send notify successed, status :{}, response: {}", response.statusCode(), response.body());
            return response;
        } catch (Exception ex) {
            log.error("send notify error , caused by {}", ex.getMessage());
            ex.printStackTrace();
        }
        return null;
    }

    private SSLContext createSSLContext() throws
            NoSuchAlgorithmException, KeyStoreException, CertificateException, IOException, KeyManagementException {
        KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
        ks.load(null, null);

        // 初始化信任管理器
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        tmf.init(ks);

        // 创建 SSLContext
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, tmf.getTrustManagers(), new java.security.SecureRandom());

        return sslContext;
    }

    public void loadCertficate(String caCertPath)
            throws CertificateException, IOException, KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        X509Certificate cert = (X509Certificate) cf.generateCertificate(Files.newInputStream(Paths.get(caCertPath)));

        KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
        ks.load(null, null);
        ks.setCertificateEntry("bochk-ca", cert);

        TrustManager[] trustManagers = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[] { cert };
                    }
                    @Override
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    @Override
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
        };
        // 更新 SSLContext
        updateSSLContext(trustManagers);
    }

    // 更新 SSLContext
    private void updateSSLContext(TrustManager[] trustManagers) throws NoSuchAlgorithmException, KeyManagementException {

        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustManagers, new java.security.SecureRandom());


        HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());

        // 更新 HttpClient 使用新的 SSLContext
        client = HttpClient.newBuilder()
                .sslContext(sslContext)
                .build();
    }
}
