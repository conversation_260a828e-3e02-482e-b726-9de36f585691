package cn.hanyi.compat.bochk.task.controller;

import cn.hanyi.compat.bochk.task.consts.DataTaskType;
import cn.hanyi.compat.bochk.task.service.DataTaskService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Hidden
@RestController
@RequestMapping("trigger")
@ConditionalOnProperty(name = "project-compat.bochk.data-task.enable", havingValue = "true")
public class TriggerController {

    @Autowired
    private DataTaskService dataTaskService;

    @Operation(summary = "手动触发数据同步任务")
    @GetMapping("data-task")
    @PreAuthorize("isAuthenticated()")
    public void dataTaskTrigger(@RequestParam(value = "taskId", required = false) Long taskId,
            @RequestParam(value = "type", required = false) DataTaskType taskType,
            @RequestParam(value = "all", required = false) Boolean allTask,
            @RequestParam(value = "do_not_upload", required = false) Boolean doNotUpload,
            @RequestParam(value = "do_not_notify", required = false) Boolean doNotNotify,
            @RequestParam(value = "trigger_date", required = false)
            @DateTimeFormat(pattern = "yyyyMMdd")
                    LocalDate triggerDate,
            @RequestParam(value = "end_date", required = false)
            @DateTimeFormat(pattern = "yyyyMMdd")
                    LocalDate endDate) {
        dataTaskService.executeTasks(taskId, taskType, allTask, triggerDate, endDate, doNotUpload, doNotNotify);
    }

}
