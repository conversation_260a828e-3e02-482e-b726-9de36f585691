package cn.hanyi.compat.bochk.task.entity;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
@Table(name = "gtb_customer_info_tmp")
@Entity
public class GTBCustomerInfoTmp extends BaseEntity {
    @Schema(description = "cbs_ac(ac68)")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "cbs_ac")
    private String cbsAC;

    @Schema(description = "cin_no")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "cin_no")
    private String cinNo;

    @Schema(description = "cbs_ac_cin_no")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "cbs_ac_cin_no")
    private String cbsAcCinNo;

    @Schema(description = "userId")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "user_id")
    private String userId;

    @Schema(description = "国家")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "bank_code")
    private String bankCode;

    @Schema(description = "使用者类型")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "stat_primary")
    private String statPrimary;

    @Schema(description = "客层")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "segment_code")
    private String segmentCode;

    @Schema(description = "指定功能")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "module_id")
    private String moduleId;

    @Schema(description = "客户经理ID")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "customer_manager_id")
    private String customerManagerId;

    @Column(name = "import_date")
    @Schema(description = "导入时间")
    private Date importDate;
}
