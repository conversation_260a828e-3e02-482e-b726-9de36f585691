package cn.hanyi.compat.bochk.task.service.impls;

import cn.hanyi.compat.bochk.task.consts.DataTaskLogStatus;
import cn.hanyi.compat.bochk.task.dto.DataTaskExecuteDto;
import cn.hanyi.compat.bochk.task.dto.SFTPConnectConfigDto;
import cn.hanyi.compat.bochk.task.entity.DataTask;
import cn.hanyi.compat.bochk.task.entity.DataTaskLog;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

@Service
public interface IFFDataService<T extends SFTPConnectConfigDto> {

    default void executeTaskAsync(DataTaskExecuteDto taskExecuteDto) {
        DataTaskLog taskLog = new DataTaskLog();
        try {
            taskLog.setTaskId(taskExecuteDto.getTask().getId());
            taskLog.setStatus(DataTaskLogStatus.RUNNING);
            updateDataTaskLogStatus(taskLog, DataTaskLogStatus.RUNNING);
            run(taskExecuteDto.getTask(), taskLog);
            updateDataTaskLogStatus(taskLog, DataTaskLogStatus.SUCCESSED);
        } catch (DataAccessException e) {
            e.printStackTrace();
            taskLog.setStatus(DataTaskLogStatus.FAILED);
            taskLog.setFailMsg("Database operation failed: " + e.getMessage());
            updateDataTaskLogStatus(taskLog, DataTaskLogStatus.FAILED);
        } catch (Exception e) {
            e.printStackTrace();
            taskLog.setStatus(DataTaskLogStatus.FAILED);
            taskLog.setFailMsg("Unexpected error: " + e.getMessage());
            updateDataTaskLogStatus(taskLog, DataTaskLogStatus.FAILED);
        }
    }

    void run(DataTask task, DataTaskLog taskLog);

    void updateDataTaskLogStatus(DataTaskLog taskLog, DataTaskLogStatus status);

    void saveFileDataToDB(T config, Path tmpFile);

//    void deleteOldTmpData(T config);

    default void deleteFile(Path filePath) throws IOException {
        if (filePath != null && Files.exists(filePath)) {
            Files.delete(filePath);
        }
    }

    default Date getImportDate(T config) {
        return Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    default Charset determineCharset(String charset) {
        // Simple logic to determine charset based on file name or content
        if (!"utf-8".equals(charset)) {
            return Charset.forName(charset);
        } else {
            return StandardCharsets.UTF_8;
        }
    }




}
