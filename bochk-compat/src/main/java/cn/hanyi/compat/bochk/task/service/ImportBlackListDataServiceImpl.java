package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.consts.DataTaskLogStatus;
import cn.hanyi.compat.bochk.task.consts.BlackListFileType;
import cn.hanyi.compat.bochk.task.dto.IFFBlackListImportConfig;
import cn.hanyi.compat.bochk.task.dto.IFFBlackListUpdateDto;
import cn.hanyi.compat.bochk.task.entity.DataTask;
import cn.hanyi.compat.bochk.task.entity.DataTaskLog;
import cn.hanyi.compat.bochk.task.entity.GtbBlackListData;
import cn.hanyi.compat.bochk.task.repository.DataTaskLogRepository;
import cn.hanyi.compat.bochk.task.repository.GtbBlackListDataRepository;
import cn.hanyi.compat.bochk.task.service.impls.IFFDataService;
import com.jcraft.jsch.ChannelSftp;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

@Service
@EnableAsync
@Slf4j
public class ImportBlackListDataServiceImpl extends SftpService implements IFFDataService<IFFBlackListImportConfig> {

    @Autowired
    private DataTaskLogRepository dataTaskLogRepository;

    @Autowired
    private BatchService batchService;

    @Autowired
    private GtbBlackListDataRepository gtbBlackListDataRepository;

    @Override
    public void run(DataTask task, DataTaskLog taskLog) {
        log.info("start execute task: {}", task.getConfig());

        IFFBlackListImportConfig config = JsonHelper.toObject(task.getConfig(), IFFBlackListImportConfig.class);
        // 执行任务
        ChannelSftp channelSftp = getSFtpConnection(config);
        List<String> fileNames = new ArrayList<>();
        try {
            fileNames.addAll(getRemoteFileList(channelSftp, config.getRemoteDir(),
                    config.getFileCUS1Prefix()));
            fileNames.addAll(getRemoteFileList(channelSftp, config.getRemoteDir(),
                    config.getFileSTAMPrefix()));
            fileNames.addAll(getRemoteFileList(channelSftp, config.getRemoteDir(),
                    config.getFileSAMPrefix()));
        } catch (Exception ex) {
            log.error("get remote file list error:{},{}",config.getRemoteDir(), ex.getMessage());
            ex.printStackTrace();
            throw new RuntimeException();
        }
        List<String> failedFileName = new ArrayList<>();

        fileNames.forEach(f -> {
            if (f.contains(config.getFileSTAMPrefix()) || f.contains(config.getFileCUS1Prefix()) || f.contains(config.getFileSAMPrefix())) {
                Path tmpFile =downloadFile(channelSftp, config.getRemoteDir(), config.getFileLocalPath(), f);
                try {
                    if (isCompressedFile(f)) {
                        List<Path> extractedFiles = extractCompressedFile(tmpFile);
                        // 处理解压后的每个文件
                        for (Path extractedFile : extractedFiles) {
                            try {
                                saveFileDataToDB(config, extractedFile);
                            } catch (Exception ex) {
                                log.error("黑名单文件{}写入数据库失败，{}",extractedFile.getFileName(),ex.getMessage());
                            } finally {
                                deleteFile(extractedFile);
                            }
                        }
                    } else {
                        saveFileDataToDB(config, tmpFile);
                    }
                } catch (IOException ex) {
                    log.error("文件操作失败{}", tmpFile);
                    failedFileName.add(f);
                    ex.printStackTrace();
                } catch (Exception ex) {
                    log.error("黑名单文件 {} 写入数据库失败,{}",f, ex.getMessage());
                    failedFileName.add(f);
                    ex.printStackTrace();
                } finally {
                    deleteFile(tmpFile);
                }
            }
        });
        batchService.deleteOldGTBBlackListTmpData(config.getDataTmpStorageDays(), config.getDataBatchDeleteLimit());
        String failedFile = String.join(";",failedFileName);
        taskLog.setFailMsg(String.format("failed file name : %s",failedFile.length() > 200 ? failedFile.substring(0, 200) : failedFile));
        log.info("---------- execute task end: {} ----------", task.getTaskType());
    }

    private boolean isCompressedFile(String fileName) {
        return fileName.endsWith(".zip") || fileName.endsWith(".tar.gz") || fileName.endsWith(".gz");
    }

    private List<Path> extractCompressedFile(Path compressedFile) throws IOException {
        List<Path> extractedFiles = new ArrayList<>();
        log.info("extract compressed file : {}", compressedFile.getFileName());

        // 判断压缩包类型，使用不同的解压方式
        if (compressedFile.toString().endsWith(".zip")) {
            try (ZipInputStream zis = new ZipInputStream(new FileInputStream(compressedFile.toFile()))) {
                ZipEntry entry;
                while ((entry = zis.getNextEntry()) != null) {
                    Path extractedFile = Path.of(compressedFile + "." + entry.getName());
                    try (FileOutputStream fos = new FileOutputStream(extractedFile.toFile())) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                    extractedFiles.add(extractedFile);
                }
            }
        } else if (compressedFile.toString().endsWith(".tar.gz")) {
            // 处理 .tar.gz 压缩包
            try (TarArchiveInputStream tarIn = new TarArchiveInputStream(
                    new GzipCompressorInputStream(new FileInputStream(compressedFile.toFile())))) {
                TarArchiveEntry entry;
                while ((entry = tarIn.getNextEntry()) != null) {
                    Path extractedFile = Path.of(compressedFile + "." + entry.getName());
                    try (FileOutputStream fos = new FileOutputStream(extractedFile.toFile())) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = tarIn.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                    extractedFiles.add(extractedFile);
                }
            }
        } else if (compressedFile.toString().endsWith(".gz")) {
            // 处理 .gz 压缩包
            try (GZIPInputStream gzipIn = new GZIPInputStream(new FileInputStream(compressedFile.toFile()))) {
                Path extractedFile = Path.of(compressedFile + ".extracted");
                try (FileOutputStream fos = new FileOutputStream(extractedFile.toFile())) {
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = gzipIn.read(buffer)) > 0) {
                        fos.write(buffer, 0, len);
                    }
                }
                extractedFiles.add(extractedFile);
            }
        }

        return extractedFiles;
    }

    @Override
    public void updateDataTaskLogStatus(DataTaskLog dataTaskLog, DataTaskLogStatus status) {
        dataTaskLog.setStatus(status);
        dataTaskLogRepository.save(dataTaskLog);
    }

    @Override
    public void saveFileDataToDB(IFFBlackListImportConfig config, Path tmpFile) {
        if (tmpFile == null || !Files.exists(tmpFile)) {
            throw new IllegalArgumentException("Invalid file path");
        }

        try (FileChannel fileChannel = FileChannel.open(tmpFile, StandardOpenOption.READ)) {
            if (tmpFile.getFileName().endsWith(config.getIngnoreFileSuffix())) {
                log.info("忽略文件：{}", tmpFile.getFileName());
            } else if (tmpFile.getFileName().toString().contains(config.getFileCUS1Prefix())) {
                processFile(config, fileChannel, BlackListFileType.CUS1);
            } else if (tmpFile.getFileName().toString().contains(config.getFileSTAMPrefix())) {
                processFile(config, fileChannel, BlackListFileType.STAM);
            } else if (tmpFile.getFileName().toString().contains(config.getFileSAMPrefix())) {
                processFile(config, fileChannel, BlackListFileType.SAM);
            } else {
                log.info("忽略文件：{}", tmpFile.getFileName());
            }
        } catch (Exception e) {
            log.error("Failed to process file");
            e.printStackTrace();
            throw new RuntimeException("Failed to process file", e);
        }
    }

    private void processFile(IFFBlackListImportConfig config, FileChannel fileChannel, BlackListFileType blackListFileType) throws IOException {
        ByteBuffer buffer = null;
        Charset charset = null;
        Integer recordLength;
        if (blackListFileType == BlackListFileType.CUS1) {
            buffer = ByteBuffer.allocate(config.getCus1RecordLength());
            charset = determineCharset(config.getFileCUS1Charset());
            recordLength = config.getCus1RecordLength();
        } else if (blackListFileType == BlackListFileType.STAM) {
            buffer = ByteBuffer.allocate(config.getStamRecordLength());
            charset = determineCharset(config.getFileSTAMCharset());
            recordLength = config.getStamRecordLength();
        } else if (blackListFileType == BlackListFileType.SAM) {
            buffer = ByteBuffer.allocate(config.getSamRecordLength());
            charset = determineCharset(config.getFileSAMCharset());
            recordLength = config.getSamRecordLength();
        } else {
            return;
        }
        log.info("start save file {} to db, {}", blackListFileType, charset);

        long position = 0;
        int batchSize = config.getImportLineBatchSize();
        int totalLine = 0;
        List<GtbBlackListData> dataList = new ArrayList<>();
        Map<String, IFFBlackListUpdateDto> upsertMap = new HashMap<>();

        while (position < fileChannel.size()) {
            fileChannel.position(position);
            buffer.flip();
            int bytesRead = fileChannel.read(buffer);

            if (bytesRead == -1) {
                break; // 文件读取完毕
            }
            buffer.clear();
            String record = charset.decode(buffer).toString();
            if (record.startsWith(config.getDataPrefix())) {
                if (blackListFileType == BlackListFileType.CUS1) {
                    processCUS1Line(config, record, upsertMap);
                } else if (blackListFileType == BlackListFileType.STAM) {
                    processSTAMLine(config, record, upsertMap);
                } else {
                    processSAMLine(config, record, upsertMap);
                }
            }
            // 批量插入
            if (upsertMap.keySet().size() >= batchSize) {
                generateUpsertLine(config, upsertMap, dataList, blackListFileType);
                gtbBlackListDataRepository.saveAll(dataList);
                totalLine+=dataList.size();
                log.info("batch save {} data: {}",blackListFileType.name(), dataList.size());
                dataList.clear();
                upsertMap.clear();
            }
            // 移动到下一条记录
            position += recordLength;
        }

        // 处理剩余的数据
        if (!upsertMap.isEmpty()) {
            generateUpsertLine(config, upsertMap, dataList, blackListFileType);
            gtbBlackListDataRepository.saveAll(dataList);
            log.info("batch save {} data: {}",blackListFileType.name(), dataList.size());
            totalLine+=dataList.size();
        }
        log.info("successed save file to db, total line {}", totalLine);
    }

    private void processSTAMLine(IFFBlackListImportConfig config, String line, Map<String, IFFBlackListUpdateDto> upsertMap) {
        if (line == null || !line.startsWith(config.getDataPrefix())) {
            // 跳过无效行
            return;
        }
        log.debug("start process stam line");

        String cbsAccount = line.substring(config.getStamEuidLeftIndex(), config.getStamEuidRightIndex()).trim();
        String statusLine = line.substring(config.getStamStatusLeftIndex(), config.getStamStatusRightIndex()).trim();
        int status = statusLine.indexOf(config.getStatusChar());
        IFFBlackListUpdateDto upsertDto = new IFFBlackListUpdateDto();
        upsertDto.setStatus(String.valueOf(status));
        upsertDto.setImportData(statusLine);
        upsertMap.put(cbsAccount, upsertDto);
    }

    private void processCUS1Line(IFFBlackListImportConfig config, String line, Map<String, IFFBlackListUpdateDto> upsertMap) {
        if (line == null || !line.startsWith(config.getDataPrefix())) {
            // 跳过无效行
            return;
        }
        log.debug("start process cus1 line");

        String cbsAccount = line.substring(config.getCus1EuidLeftIndex(), config.getCus1EuidRightIndex()).trim();
        String branchNo = line.substring(config.getCus1BranchNoLeftIndex(), config.getCus1BranchNoRightIndex()).trim();
        String type = line.substring(config.getCus1TypeLeftIndex(), config.getCus1TypeRightIndex()).trim();
        if (!config.getCus1OrgType().equals(type)) {
            return;
        }
        IFFBlackListUpdateDto upsertDto = new IFFBlackListUpdateDto();
        upsertDto.setEuid(cbsAccount);
        upsertDto.setBranchNo(branchNo);
        upsertDto.setType(type);
        upsertMap.put(cbsAccount, upsertDto);
    }

    private void processSAMLine(IFFBlackListImportConfig config, String line, Map<String, IFFBlackListUpdateDto> updateMap) {
        if (line == null || !line.startsWith(config.getDataPrefix())) {
            // 跳过无效行
            return;
        }
        log.debug("start process sam line");

        String cbsAccount = line.substring(config.getSamEuidLeftIndex(), config.getSamEuidRightIndex()).trim();
        String deptCode = line.substring(config.getSamDeptCodeLeftIndex(), config.getSamDeptCodeRightIndex()).trim();
        String guarInd = line.substring(config.getSamGuarIndLeftIndex(), config.getSamGuarIndRightIndex()).trim();
        IFFBlackListUpdateDto updateDto = new IFFBlackListUpdateDto();
        updateDto.setDeptCode(deptCode);
        updateDto.setGuarInd(guarInd);
        updateMap.put(cbsAccount, updateDto);
    }

    private void generateUpsertLine(IFFBlackListImportConfig config, Map<String, IFFBlackListUpdateDto> branchNoMap, List<GtbBlackListData> dataList, BlackListFileType blackListFileType) {
        if (branchNoMap != null && !branchNoMap.isEmpty()) {
            Date importDate = getImportDate(config);
            List<GtbBlackListData> blackListData = gtbBlackListDataRepository.findAllByExternalUserIdInAndImportDate(branchNoMap.keySet(), importDate);
            Map<String, GtbBlackListData> existedBlackDataMap = blackListData.stream().collect(
                    Collectors.toMap(GtbBlackListData::getExternalUserId, Function.identity(), (existing, replacement) -> existing));
            for(Map.Entry<String, IFFBlackListUpdateDto> entry : branchNoMap.entrySet()) {
                GtbBlackListData blackData = new GtbBlackListData();

                if (existedBlackDataMap.containsKey(entry.getKey())) {
                    blackData = existedBlackDataMap.get(entry.getKey());
                } else if (blackListFileType != BlackListFileType.CUS1) {
                    // STAM文件和SAM文件只做更新
                    continue;
                }
                blackData.setExternalUserId(entry.getKey());
                if (blackListFileType == BlackListFileType.CUS1) {
                    blackData.setBranchNo(entry.getValue().getBranchNo());
                    blackData.setType(entry.getValue().getType());
                } else if (blackListFileType == BlackListFileType.STAM) {
                    blackData.setStatus(entry.getValue().getStatus());
                    blackData.setImportData(entry.getValue().getImportData());
                } else if (blackListFileType == BlackListFileType.SAM) {
                    blackData.setDeptCode(entry.getValue().getDeptCode());
                    blackData.setGuarInd(entry.getValue().getGuarInd());
                }
                blackData.setImportDate(getImportDate(config));
                dataList.add(blackData);
            }
        }
    }

    @Override
    public void deleteFile(Path filePath) {
        try {
            if (filePath != null && Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("删除临时文件 {} 成功", filePath.getFileName());
            }
        } catch (Exception ex) {
            log.error("删除临时文件 {} 失败，{}", filePath != null ? filePath.getFileName() : "null", ex.getMessage());
        }

    }
}
