package cn.hanyi.compat.bochk.task.service.impls;

import cn.hanyi.compat.bochk.task.consts.DataTaskLogStatus;
import cn.hanyi.compat.bochk.task.dto.DataExportConfig;
import cn.hanyi.compat.bochk.task.dto.DataTaskExecuteDto;
import cn.hanyi.compat.bochk.task.entity.DataTaskLog;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.survey.core.entity.Survey;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.function.BiFunction;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.rest.context.TenantContext;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

@Service
public interface IFFExportDataService<T extends DataExportConfig> {

    default void executeTaskAsync(DataTaskExecuteDto dto) {
        DataTaskLog taskLog = new DataTaskLog();
        try {
            taskLog.setTaskId(dto.getTask().getId());
            taskLog.setStatus(DataTaskLogStatus.RUNNING);
            updateDataTaskLogStatus(taskLog, DataTaskLogStatus.RUNNING);
            run(dto, taskLog);
            updateDataTaskLogStatus(taskLog, DataTaskLogStatus.SUCCESSED);
        } catch (DataAccessException e) {
            e.printStackTrace();
            taskLog.setStatus(DataTaskLogStatus.FAILED);
            taskLog.setFailMsg("Database operation failed: " + e.getMessage());
            updateDataTaskLogStatus(taskLog, DataTaskLogStatus.FAILED);
        } catch (Exception e) {
            e.printStackTrace();
            taskLog.setStatus(DataTaskLogStatus.FAILED);
            taskLog.setFailMsg("Unexpected error: " + e.getMessage());
            updateDataTaskLogStatus(taskLog, DataTaskLogStatus.FAILED);
        }
    }

    void run(DataTaskExecuteDto taskExecuteDto, DataTaskLog taskLog);

    default Path exportData(T config, LocalDate triggerDate, LocalDate endDate) {
        Path tempFile = null;
        BufferedWriter writer = null;
        try {
            Files.deleteIfExists(Paths.get(config.getFileLocalPath(), config.getFileName()));
            tempFile = Files.createFile(Paths.get(config.getFileLocalPath(), config.getFileName()));
            writer = Files.newBufferedWriter(tempFile, StandardCharsets.UTF_8);

            buildFirstRowData(config, writer);
            // Export survey data
            TenantContext.addCustomEntityScopeStrategy(Survey.class, EntityScopeStrategyType.NONE);
            TenantContext.addCustomEntityScopeStrategy(SendManage.class, EntityScopeStrategyType.NONE);
            Long dataCount = writeTableData(config, writer, triggerDate, endDate);

            buildEndRowData(config, dataCount, writer, triggerDate);

        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("Failed to write survey data to file", e);
        } finally {
            closeWriter(writer);
        }
        return tempFile;
    }

    Long writeTableData(T config, BufferedWriter writer, LocalDate triggerDate, LocalDate endDate) throws IOException;

    void updateDataTaskLogStatus(DataTaskLog taskLog, DataTaskLogStatus status);

    default void buildFirstRowData(T config, BufferedWriter writer)
            throws IOException {
        StringBuilder row = new StringBuilder();

        row.append(formatString(config.getFirstRowDataPrefix(), config.getRowLength(),
                config.getRowFillChar(), config));
        writer.write(row.toString());
        writer.newLine();
    }

    default Long exportPaginatedData(T config, BufferedWriter writer,LocalDate date, BiFunction<Integer,LocalDate, List<?>> fetchPageData) throws IOException {
        int page = 0;
        int limit = config.getPageLimit();
        boolean hasNext = true;
        Long count = 0L;

        while (hasNext) {
            List<?> pageData = fetchPageData.apply(page, date);

            for (Object data : pageData) {
                String rowData = buildRowData(config, data);
                writer.write(rowData);
                count += 1;
                writer.newLine();
            }

            hasNext = pageData.size() >= limit;
            page++;
        }
        return count;
    }

    String buildRowData(T config, Object dto);

    // 计算字符串在终端中占用的列数
    default int getTerminalColumnLength(String str, T config) {
        if (str == null) {
            return 0;
        }
        byte[] utf8Bytes = str.getBytes(StandardCharsets.UTF_8);
        return utf8Bytes.length;
//        int length = 0;
//        int strLength = str.length();
//
//        for (int i = 0; i < strLength; ) {
//            // 获取当前码点(支持 Unicode 代理对)
//            int codePoint = str.codePointAt(i);
//
//            if (isWideCharacter(codePoint, config)) {
//                length += config.getWideCharacterLength();
//            } else {
//                length += 1;
//            }
//
//            // 移动到下一个字符，处理代理对
//            i += Character.charCount(codePoint);
//        }
//
//        return length;
    }

    default boolean isWideCharacter(int codePoint, T config) {
        // 处理空白字符
        if (Character.isWhitespace(codePoint)) {
            return false;
        }

        // 获取字符的 Unicode 块
        Character.UnicodeBlock block = Character.UnicodeBlock.of(codePoint);

        // 检查是否是 Emoji 字符
        // Emoji 表情符号的��围
        boolean isEmoji = (codePoint >= 0x1F300 && codePoint <= 0x1F9FF) ||  // Miscellaneous Symbols and Pictographs
                (codePoint >= 0x2600 && codePoint <= 0x26FF) ||      // Miscellaneous Symbols
                (codePoint >= 0x2700 && codePoint <= 0x27BF) ||      // Dingbats
                (codePoint >= 0x1F000 && codePoint <= 0x1F02F) ||    // Mahjong Tiles
                (codePoint >= 0x1F0A0 && codePoint <= 0x1F0FF) ||    // Playing Cards
                (codePoint >= 0x1F100 && codePoint <= 0x1F1FF) ||    // Enclosed Alphanumeric Supplement
                (codePoint >= 0x1F200 && codePoint <= 0x1F2FF);      // Enclosed Ideographic Supplement

        if (isEmoji) {
            return true;
        }

        // 检查是否是 CJK 相关字符
        return block != null && (
                // CJK统一表意文字
                block == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS ||
                        // CJK统一表意文字扩展
                        block == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A ||
                        block == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B ||
                        // 日文平假名和片假名
                        block == Character.UnicodeBlock.HIRAGANA ||
                        block == Character.UnicodeBlock.KATAKANA ||
                        // 全角字符和半角字符
                        block == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS ||
                        // CJK符号和标点
                        block == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION ||
                        // 在配置的范围内
                        (codePoint >= config.getWideCharacterLeftBound() &&
                                codePoint <= config.getWideCharacterRightBound())
        );
    }

    default String formatString(String input, int length, String fillChar, T config) {
        if (input == null) {
            return fillChar.repeat(length);
        }
        int inputLength = getTerminalColumnLength(input, config);
        if (inputLength < length) {
            return input + fillChar.repeat(length - inputLength);
        } else if (inputLength > length) {
            return truncateStringToMaxColumns(input, length, config);
        }
        return input;
    }

    default void closeWriter(BufferedWriter writer) {
        if (writer != null) {
            try {
                writer.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    default String truncateStringToMaxColumns(String str, int maxColumns, T config) {
        StringBuilder sb = new StringBuilder();
        int columnCount = 0;

        for (int i = 0; i < str.length(); i++) {
            char ch = str.charAt(i);
            int charWidth = isWideCharacter(ch, config) ? config.getWideCharacterLength() : 1;

            // 如果当前列数超过最大列数，停止添加
            if (columnCount + charWidth > maxColumns) {
                break;
            }

            sb.append(ch);
            columnCount += charWidth;
        }

        return sb.toString();
    }

    default void buildEndRowData(T config, Long total, BufferedWriter writer, LocalDate triggerDate)
            throws IOException {
        StringBuilder row = new StringBuilder();
        if (triggerDate == null) {
            triggerDate = LocalDate.now().minusDays(1);
        }
        LocalDateTime currentDate = LocalDateTime.now();

        row.append(config.getEndDataPrefix());
        row.append(triggerDate.format(DateTimeFormatter.ofPattern(config.getEndRowDateFormatter())));
        row.append(currentDate.format(DateTimeFormatter.ofPattern(config.getEndRowtimeFormatter())));
        row.append(String.format("%09d", Math.min(total, config.getMaxDataCount())));
        writer.write(formatString(row.toString(), config.getRowLength(), config.getRowFillChar(), config));
        writer.newLine();
    }
}
