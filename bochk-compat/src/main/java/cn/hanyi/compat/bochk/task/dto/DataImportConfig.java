package cn.hanyi.compat.bochk.task.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DataImportConfig extends SFTPConnectConfigDto {
    private String fileLocalPath = "/tmp/files/";
    private String remoteDir = "";
    private String dataPrefix = "1";
    private String importDateFormatter = "yyyyMMdd";
    private Integer importLineBatchSize = 1000;
    private Integer dataTmpStorageDays = 15;
    private Integer dataBatchDeleteLimit = 5000;

}
