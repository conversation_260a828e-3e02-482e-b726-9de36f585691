package cn.hanyi.compat.bochk.task.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class IFFBlackListImportConfig extends DataImportConfig {

    private String fileSTAMPrefix = "ISB.DLD.CCI.CCI.STAM.";
    private Integer stamRecordLength = 374;
    private Integer stamEuidLeftIndex = 20;
    private Integer stamEuidRightIndex = 30;
    private Integer stamStatusLeftIndex = 31;
    private Integer stamStatusRightIndex = 351;
    private String fileSTAMCharset = "IBM1047";


    private String fileCUS1Prefix = "ISB.DLD.CCI.CCI.CUS1.";
    private Integer cus1RecordLength = 332;
    private Integer cus1EuidLeftIndex = 19;
    private Integer cus1EuidRightIndex = 29;
    private Integer cus1BranchNoLeftIndex = 155;
    private Integer cus1BranchNoRightIndex = 160;
    private Integer cus1TypeLeftIndex = 106;
    private Integer cus1TypeRightIndex = 108;
    private String cus1OrgType = "02";
    private String fileCUS1Charset = "IBM1047";

    private String fileSAMPrefix = "SAM_CCI_C03.TXT";
    private Integer samRecordLength = 130;
    private Integer samEuidLeftIndex = 4;
    private Integer samEuidRightIndex = 14;
    private Integer samDeptCodeLeftIndex = 14;
    private Integer samDeptCodeRightIndex = 17;
    private Integer samGuarIndLeftIndex = 24;
    private Integer samGuarIndRightIndex = 25;
    private String fileSAMCharset = "utf-8";

    private String statusChar = "1";
    private String fileCharset = "IBM1047";
    private String ingnoreFileSuffix = ".gz";


}
