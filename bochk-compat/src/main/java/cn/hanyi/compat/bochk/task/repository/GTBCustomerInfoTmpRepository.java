package cn.hanyi.compat.bochk.task.repository;

import cn.hanyi.compat.bochk.task.entity.GTBCustomerInfoTmp;

import java.util.Date;
import java.util.List;
import java.util.Set;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface GTBCustomerInfoTmpRepository extends ResourceRepository<GTBCustomerInfoTmp, Long> {
    List<GTBCustomerInfoTmp> findAllByImportDateAndCbsAcCinNoIn(Date importDate, Set<String> cbsAcCinNo);
    List<GTBCustomerInfoTmp> findAllByImportDateAndCinNoIn(Date importDate, Set<String> cinNo);
    List<GTBCustomerInfoTmp> findAllByImportDateAndCbsACIn(Date importDate, Set<String> cbsAc);

    @Modifying
    @Query(value = "DELETE FROM gtb_customer_info_tmp WHERE import_date < ?1 LIMIT ?2", nativeQuery = true)
    int deleteBatchByImportDateBefore(Date importDate, int batchSize);

    List<GTBCustomerInfoTmp> findAllByImportDateAndIdGreaterThan(Date importDate, Long id, Pageable pageable);

}
