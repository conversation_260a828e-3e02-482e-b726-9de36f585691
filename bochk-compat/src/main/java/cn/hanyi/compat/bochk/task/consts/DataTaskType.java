package cn.hanyi.compat.bochk.task.consts;

import cn.hanyi.compat.bochk.task.dto.DataExportConfig;
import cn.hanyi.compat.bochk.task.dto.DataImportConfig;
import cn.hanyi.compat.bochk.task.dto.IFFCustomerInfoImportConfig;
import cn.hanyi.compat.bochk.task.dto.IFFSurveyResponseDataExportConfig;
import cn.hanyi.compat.bochk.task.dto.SurveySendIFFRecordExportConfig;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum DataTaskType {

    // 导出任务
    EXPORT_DATA_TASK(DataExportConfig.class, ""),
    IMPORT_GTBCOUNT_DATA_TASK(DataImportConfig.class, ""),
    IMPORT_BLACKLIST_DATA_TASK(DataImportConfig.class, ""),
    EXPORT_SURVEY_RESPONSE_DATA_TASK(IFFSurveyResponseDataExportConfig.class, ""),
    EXPORT_SURVEY_SEND_DATA_TASK(SurveySendIFFRecordExportConfig .class, ""),
    IMPORT_CUSTOMER_INFO_TASK(IFFCustomerInfoImportConfig.class, ""),
;
    private final Class<?> paramClass;
    private final String triggerClassName;

    DataTaskType(Class<?> paramClass, String triggerClassName) {
        this.paramClass = paramClass;
        this.triggerClassName = triggerClassName;
    }
}
