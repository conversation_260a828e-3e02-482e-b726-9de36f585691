package cn.hanyi.compat.bochk.task.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DataExportNotifyConfig {
    /**
     * notify信息
     */
    private ReqHeader reqHeader = new ReqHeader();
    private ReqBody reqBody = new ReqBody();

    @Getter
    @Setter
    public static class ReqHeader {
        private String channelCode = "000001";
        private String callCode = "VHKUEM0000000";
    }

    @Getter
    @Setter
    public static class ReqBody {
        private String iffId = "CLUEM00001";
        private String datasetName = "SURVEY_ENABLE.TXT";
        private String srcApp = "CLUEM";
        private String acDate;
        private String refKey;
    }
}
