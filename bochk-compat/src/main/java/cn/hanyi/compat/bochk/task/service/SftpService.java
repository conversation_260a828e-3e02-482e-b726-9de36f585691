package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.dto.DataExportConfig;
import cn.hanyi.compat.bochk.task.dto.SFTPConnectConfigDto;
import cn.hutool.core.lang.Assert;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Vector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hpsf.Decimal;
import org.befun.core.exception.BadRequestException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SftpService {

    public ChannelSftp getSFtpConnection(SFTPConnectConfigDto config) {
        ChannelSftp channelSftp = null;
        Session session = null;
        try {
            JSch jsch = new JSch();
            if (StringUtils.isEmpty(config.getSftpPassword())) {
                jsch.addIdentity(config.getPrivateKeyPath());
            }
            session = jsch.getSession(config.getSftpUser(), config.getSftpHost(), config.getSftpPort());
            session.setPassword(config.getSftpPassword());
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect();
            session.setTimeout(config.getSessionTimeOut());
            log.info("session连接成功");

            channelSftp = (ChannelSftp) session.openChannel("sftp");
            channelSftp.connect();
            log.info("channelSftp连接成功");
            return channelSftp;
        } catch (JSchException e) {
            log.error("连接服务器失败，username:{},host:{},port:{},caused by {}", config.getSftpUser(), config.getSftpHost(), config.getSftpPort(), e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    public void uploadFile(Path tempFile, DataExportConfig config) {
        ChannelSftp channelSftp = getSFtpConnection(config);
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(tempFile.toFile());
            for (String dir : config.getRemoteDir()) {
                try {
                    channelSftp.cd(dir);
                    log.info("Directory exists: {}", dir);
                } catch (SftpException e) {
                    log.warn("Directory does not exist, creating: {}", dir);
                    channelSftp.mkdir(dir);
                    channelSftp.cd(dir);
                }
                // 上传文件到当前目录
                channelSftp.put(fis, config.getFileName());
                log.info("File uploaded successfully, {}", dir + config.getFileName());
                fis.close();
                fis = new FileInputStream(tempFile.toFile());
            }
            fis.close();

        } catch (SftpException | IOException e) {
            e.printStackTrace();
            log.error("上传文件失败，username:{},host:{},port:{},caused by {}", config.getSftpUser(), config.getSftpHost(), config.getSftpPort(), e.getMessage());
            throw new BadRequestException(e.getMessage());
        } finally {
            try {
                if (fis != null) {
                    fis.close();
                }
                Files.delete(tempFile);
            } catch (IOException e) {
                log.warn("Failed to delete temporary file: {}", tempFile, e);
            }
        }

    }

    public Path downloadFile(ChannelSftp channelSftp, String remoteDir, String fileLocalPath,
            String fileName) {
        try {
            channelSftp.cd(remoteDir);
            log.info("Directory exists: {}, find file : {}", remoteDir, fileName);
            Path localFilePath = Paths.get(fileLocalPath, fileName);
            log.info("Local file path:, {}", localFilePath);
            try (OutputStream outputStream = new FileOutputStream(localFilePath.toFile())) {
                channelSftp.get(fileName, outputStream);
                log.info("Downloaded: " + fileName);
            } catch (IOException e) {
                log.error("File not found, {}", fileName);
                throw new RuntimeException(e);
            }
            return localFilePath;

        } catch (SftpException e) {
            log.error("Directory does not exist: {}", remoteDir);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取远程服务器path所有文件路径
     *
     * @param channelSftp 连接通道
     * @param dirPath 目录路径
     * @param filePrefix 文件前缀
     * @return 符合条件的文件路径列表
     */
    public List<String> getRemoteFileList(ChannelSftp channelSftp, String dirPath, String filePrefix) {
        Assert.notNull(channelSftp, "channelSftp cannot be null");
        Assert.notBlank(dirPath, "dirPath cannot be empty or null");

        List<String> result = new ArrayList<>();

        // 确保路径以斜杠结尾
        if (!dirPath.endsWith("/")) {
            dirPath += "/";
        }

        Vector<LsEntry> files;
        try {
            files = channelSftp.ls(dirPath);
        } catch (SftpException e) {
            log.error("Invalid path: path={}", dirPath, e);
            throw new BadRequestException("Failed to list files in the specified path",
                    String.valueOf(e));
        }

        // 如果没有文件，直接返回空列表
        if (files == null || files.isEmpty()) {
            return result;
        }

        // 遍历文件列表，过滤符合前缀条件的文件
        for (LsEntry entry : files) {
            String filename = entry.getFilename();
            if (filename.contains(filePrefix)) {
                result.add(filename);
            }
        }

        log.info("Found {} files with prefix '{}': {}", result.size(), filePrefix, result);
        return result;
    }

    public void printProgress(long processedBytes, long totalFileSize, int recordCount, long startTime) {
        long currentTime = System.currentTimeMillis();
        long elapsedTime = currentTime - startTime;

        if (totalFileSize > 0) {
            double progress = (double) processedBytes / totalFileSize * 100;
            double speed = (double) processedBytes / (elapsedTime / 1000.0);
            String progressPercent = String.format("%.2f", progress);

            log.info("进度: {}/{} ({}%), 记录: {}, 速度: {}/s, 耗时: {}",
                    formatFileSize(processedBytes),
                    formatFileSize(totalFileSize),
                    progressPercent,
                    recordCount,
                    formatFileSize((long) speed),
                    formatDuration(elapsedTime));
        } else {
            log.info("已处理: {}, 记录: {}, 耗时: {}",
                    formatFileSize(processedBytes),
                    recordCount,
                    formatDuration(elapsedTime));
        }
    }

    /**
     * 格式化文件大小（人性化显示）
     */
    public String formatFileSize(long size) {
        if (size <= 0) return "0 B";

        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));

        return String.format("%.2f %s",
                size / Math.pow(1024, digitGroups),
                units[digitGroups]);
    }

    /**
     * 格式化时间间隔（人性化显示）
     */
    public String formatDuration(long milliseconds) {
        if (milliseconds < 1000) {
            return milliseconds + " ms";
        }

        long seconds = milliseconds / 1000;
        if (seconds < 60) {
            return String.format("%.1f s", milliseconds / 1000.0);
        }

        long minutes = seconds / 60;
        seconds = seconds % 60;

        if (minutes < 60) {
            return String.format("%d m %d s", minutes, seconds);
        }

        long hours = minutes / 60;
        minutes = minutes % 60;

        return String.format("%d h %d m %d s", hours, minutes, seconds);
    }

    /**
     * 显示最终完成进度
     */
    public void printFinalProgress(long processedBytes, long totalFileSize,
            int recordCount, int totalLine, long startTime) {
        long totalTime = System.currentTimeMillis() - startTime;
        double speed = (double) processedBytes / (totalTime / 1000.0);

        log.info("==================================================");
        log.info("文件处理完成!");
        log.info("总大小: {}", formatFileSize(totalFileSize));
        log.info("总记录数: {}", recordCount);
        log.info("有效数据行: {}", totalLine);
        log.info("总耗时: {}", formatDuration(totalTime));
        log.info("平均速度: {}/s", formatFileSize((long) speed));

        if (totalTime > 0) {
            log.info("处理性能: {}/s",
                    String.format("%.1f", (double) recordCount / (totalTime / 1000.0)));
        }
        log.info("==================================================");
    }

}
