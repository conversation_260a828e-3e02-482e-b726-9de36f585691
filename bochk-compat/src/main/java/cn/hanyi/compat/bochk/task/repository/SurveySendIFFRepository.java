package cn.hanyi.compat.bochk.task.repository;

import cn.hanyi.compat.bochk.task.entity.SurveySendIFFRecord;
import java.util.Date;
import java.util.List;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;

public interface SurveySendIFFRepository extends ResourceRepository<SurveySendIFFRecord, Long>{
    List<SurveySendIFFRecord> findAllByCreateTimeBetween(Date startTime, Date endTime, Pageable pageable);
}
