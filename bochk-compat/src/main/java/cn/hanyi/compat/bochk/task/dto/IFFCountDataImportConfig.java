package cn.hanyi.compat.bochk.task.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class IFFCountDataImportConfig extends DataImportConfig {
    private String fileName = "GTBBDPJNL";
    private Integer txCodeLeftIndex = 12;
    private Integer txCodeRightIndex = 17;
    private Integer euidLeftIndex = 22;
    private Integer euidRightIndex = 36;
    private String fileCharset = "utf-8";
    private Integer recordLength = 2186;
    private Boolean enableIncrement = false;

}
