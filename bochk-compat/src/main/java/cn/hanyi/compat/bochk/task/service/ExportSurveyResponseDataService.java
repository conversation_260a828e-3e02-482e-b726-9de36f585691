package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.consts.DataTaskLogStatus;
import cn.hanyi.compat.bochk.task.dto.DataTaskExecuteDto;
import cn.hanyi.compat.bochk.task.dto.IFFSurveyResponseDataExportConfig;
import cn.hanyi.compat.bochk.task.dto.SimpleExportQuestion;
import cn.hanyi.compat.bochk.task.dto.SimpleExportQuestionColumn;
import cn.hanyi.compat.bochk.task.dto.SimpleExportQuestionItem;
import cn.hanyi.compat.bochk.task.dto.SimpleExportResponse;
import cn.hanyi.compat.bochk.task.dto.SimpleExportResponseCell;
import cn.hanyi.compat.bochk.task.entity.DataTaskLog;
import cn.hanyi.compat.bochk.task.repository.DataTaskLogRepository;
import cn.hanyi.compat.bochk.task.service.impls.IFFExportDataService;
import cn.hanyi.survey.core.repository.SurveyQuestionColumnRepository;
import cn.hanyi.survey.core.repository.SurveyQuestionItemRepository;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import java.io.BufferedWriter;
import java.io.IOException;
import java.net.http.HttpResponse;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.shade.io.netty.handler.codec.http.HttpResponseStatus;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ExportSurveyResponseDataService extends SftpService implements
        IFFExportDataService<IFFSurveyResponseDataExportConfig> {

    @Autowired
    DataTaskLogRepository dataTaskLogRepository;

    @Autowired
    SurveyResponseRepository surveyResponseRepository;

    @Autowired
    SurveyResponseCellRepository surveyResponseCellRepository;

    @Autowired
    SurveyQuestionRepository surveyQuestionRepository;

    @Autowired
    SurveyQuestionItemRepository surveyQuestionItemRepository;

    @Autowired
    SurveyQuestionColumnRepository surveyQuestionColumnRepository;

    @Autowired
    private NativeSqlHelper nativeSqlHelper;

    @Autowired
    private NotifyService notifyService;

    @Override
    public void run(DataTaskExecuteDto dto, DataTaskLog taskLog) {
        log.info("----------- start execute task: {} -----------", dto.getTask().getConfig());
        try {
            IFFSurveyResponseDataExportConfig config = JsonHelper
                    .toObject(dto.getTask().getConfig(), IFFSurveyResponseDataExportConfig.class);
            LocalDate exportDate = LocalDate.now().minusDays(config.getMinusDays());
            LocalDate endDate = dto.getEndDate();
            if (dto.getTriggerDate() != null) {
                exportDate = dto.getTriggerDate();
            }
            if (endDate == null) {
                endDate = exportDate.plusDays(config.getPlusDays());
            }
            Path tempFile = exportData(config, exportDate, endDate);
            if (!dto.getDoNotUpload()) {
                uploadFile(tempFile, config);
            }
            if (!dto.getDoNotNotify()) {
                HttpResponse<String> response = notifyService.sendNotify(config,exportDate);
                if (response.statusCode() == HttpResponseStatus.OK.code()) {
                    taskLog.setStatus(DataTaskLogStatus.SUCCESSED);
                } else {
                    taskLog.setStatus(DataTaskLogStatus.FAILED);
                }
                taskLog.setFailMsg(String.valueOf(response.statusCode()));
                dataTaskLogRepository.save(taskLog);
            }
        } catch (Exception e) {
            // 异常日志记录
            log.error("execute task error: caused by: {}", e.getMessage());
            e.printStackTrace();
            taskLog.setStatus(DataTaskLogStatus.FAILED);
            taskLog.setFailMsg(e.getMessage());
            dataTaskLogRepository.save(taskLog);
        }
        log.info("---------- execute task end: {} ----------", dto.getTask().getTaskType());

    }

    @Override
    public Long writeTableData(IFFSurveyResponseDataExportConfig config, BufferedWriter writer, LocalDate triggerDate, LocalDate endDate) throws IOException {

        Long responseCount = exportPaginatedData(config, writer, triggerDate, (page,date) -> findAllSimpleResponse(PageRequest.of(page, config.getPageLimit()), date, endDate));
        Long responseCellCount = exportPaginatedData(config, writer, triggerDate, (page,date) -> findAllSimpleResponseCell(config, PageRequest.of(page, config.getPageLimit()), date, endDate));
        Long surveyQuestionCount = exportPaginatedData(config, writer, triggerDate, (page,date) -> findAllSimpleQuestion(PageRequest.of(page, config.getPageLimit()), date, endDate));
        Long surveyQuestionItemCount = exportPaginatedData(config, writer, triggerDate, (page,date) -> findAllSimpleQuestionItem(PageRequest.of(page, config.getPageLimit()), date, endDate));
        Long surveyQuestionColumnCount = exportPaginatedData(config, writer, triggerDate, (page,date) -> findAllSimpleQuestionColumn(PageRequest.of(page, config.getPageLimit()), date, endDate));
        return responseCount + responseCellCount + surveyQuestionCount + surveyQuestionItemCount + surveyQuestionColumnCount;
    }

    @Override
    public String buildRowData(IFFSurveyResponseDataExportConfig config, Object dto) {
        if (dto instanceof SimpleExportResponse) {
            return buildSurveyResponseRowData(config, (SimpleExportResponse) dto);
        } else if (dto instanceof SimpleExportResponseCell) {
            return buildSurveyResponseCellRowData(config, (SimpleExportResponseCell) dto);
        } else if (dto instanceof SimpleExportQuestion) {
            return buildSurveyQuestionData(config, (SimpleExportQuestion) dto);
        } else if (dto instanceof SimpleExportQuestionItem) {
            return buildSurveyQuestionItemData(config, (SimpleExportQuestionItem) dto);
        } else if (dto instanceof SimpleExportQuestionColumn) {
            return buildSurveyQuestionColumnData(config, (SimpleExportQuestionColumn) dto);
        }
        throw new IllegalArgumentException("Unsupported DTO type");
    }

    private String buildSurveyResponseRowData(IFFSurveyResponseDataExportConfig config, SimpleExportResponse response) {
        SimpleDateFormat sdf = new SimpleDateFormat(config.getDataDateFormatter());
        StringBuilder row = new StringBuilder();
        row.append(config.getRowDataPrefix());
        row.append(formatString(Objects.toString(response.getId(),null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(response.getSid(), null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(response.getEuid(), null), config.getResponseEuidLentgh(), config.getSurveyFillChar(), config));
        row.append(formatString(response.getCreateTime() == null ? null : sdf.format(response.getCreateTime()), config.getCreateTimeLength(), config.getSurveyFillChar(), config));
        row.append(formatString(response.getCollectorMethod() == null ? null : String.valueOf(response.getCollectorMethod().ordinal()), config.getResponseCollectorMethodLength(), config.getSurveyFillChar(), config));
        row.append(formatString(response.getFinishTime() == null ? null :sdf.format(response.getFinishTime()), config.getResponseFinishTimeLength(), config.getSurveyFillChar(), config));
        row.append(formatString(response.getStatus() == null ? null : String.valueOf(response.getStatus()), config.getResponseStatusLength(), config.getSurveyFillChar(), config));
        Map<String, Object> parameters = JsonHelper.toMap(response.getParameters());
        for (String key : config.getParameterKeys()) {
            row.append(formatString(Objects.toString(parameters.get(key), null), config.getResponseParameterLength(), config.getSurveyFillChar(), config));
        }
        return formatString(row.toString(), config.getRowLength(), config.getRowFillChar(), config);
    }

    private String buildSurveyResponseCellRowData(IFFSurveyResponseDataExportConfig config, SimpleExportResponseCell responseCell) {
        SimpleDateFormat sdf = new SimpleDateFormat(config.getDataDateFormatter());
        StringBuilder row = new StringBuilder();
        row.append(config.getRowDataPrefix());
        row.append(formatString("", config.getResponseRowLength(), config.getRowFillChar(), config));
        row.append(formatString(Objects.toString(responseCell.getId(),null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(responseCell.getSid(), null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(responseCell.getRid(), null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(responseCell.getQid(), null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(responseCell.getType(), null), config.getResponseCellTypeLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(responseCell.getIVal(), null), config.getResponseCellIValLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(responseCell.getDVal(), null), config.getResponseCellDValLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(responseCell.getSVal(), null), config.getResponseCellSValLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(responseCell.getJVal(), null), config.getResponseCellJValLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(responseCell.getCommentVal(), null), config.getResponseCellCommentValLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(responseCell.getTVal(), null), config.getResponseCellTValLength(), config.getSurveyFillChar(), config));
        row.append(formatString(responseCell.getCreateTime() == null ? null : sdf.format(responseCell.getCreateTime()), config.getCreateTimeLength(), config.getSurveyFillChar(), config));
        row.append(formatString(responseCell.getModifyTime() == null ? null : sdf.format(responseCell.getModifyTime()), config.getModifyTimeLength(), config.getSurveyFillChar(), config));

        return formatString(row.toString(), config.getRowLength(), config.getRowFillChar(), config);
    }

    private String buildSurveyQuestionData(IFFSurveyResponseDataExportConfig config, SimpleExportQuestion question) {
        SimpleDateFormat sdf = new SimpleDateFormat(config.getDataDateFormatter());
        StringBuilder row = new StringBuilder();
        row.append(config.getRowDataPrefix());
        row.append(formatString("", config.getResponseRowLength(), config.getRowFillChar(), config));
        row.append(formatString("", config.getResponseCellRowLength(), config.getRowFillChar(), config));
        row.append(formatString(Objects.toString(question.getId(),null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(question.getSid(), null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(question.getName(), null), config.getQuestionNameLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(question.getTitle(), null), config.getQuestionTitleLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(question.getType(), null), config.getQuestionTypeLength(), config.getSurveyFillChar(), config));
        row.append(formatString(question.getCreateTime() == null ? null : sdf.format(question.getCreateTime()), config.getCreateTimeLength(), config.getSurveyFillChar(), config));
        row.append(formatString(question.getModifyTime() == null ? null : sdf.format(question.getModifyTime()), config.getModifyTimeLength(), config.getSurveyFillChar(), config));
        return formatString(row.toString(), config.getRowLength(), config.getRowFillChar(), config);
    }


    private String buildSurveyQuestionItemData(IFFSurveyResponseDataExportConfig config, SimpleExportQuestionItem questionItem) {
        SimpleDateFormat sdf = new SimpleDateFormat(config.getDataDateFormatter());
        StringBuilder row = new StringBuilder();
        row.append(config.getRowDataPrefix());
        row.append(formatString("", config.getResponseRowLength(), config.getRowFillChar(), config));
        row.append(formatString("", config.getResponseCellRowLength(), config.getRowFillChar(), config));
        row.append(formatString("", config.getQuestionRowLength(), config.getRowFillChar(), config));
        row.append(formatString(Objects.toString(questionItem.getId(),null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(questionItem.getQid(), null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(questionItem.getValue(),null), config.getResponseEuidLentgh(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(questionItem.getText(),null), config.getResponseEuidLentgh(), config.getSurveyFillChar(), config));
        row.append(formatString(questionItem.getCreateTime() == null ? null : sdf.format(questionItem.getCreateTime()), config.getCreateTimeLength(), config.getSurveyFillChar(), config));
        row.append(formatString(questionItem.getModifyTime() == null ? null : sdf.format(questionItem.getModifyTime()), config.getModifyTimeLength(), config.getSurveyFillChar(), config));
        return formatString(row.toString(), config.getRowLength(), config.getRowFillChar(), config);
    }

    private String buildSurveyQuestionColumnData(IFFSurveyResponseDataExportConfig config, SimpleExportQuestionColumn questionColumn) {
        SimpleDateFormat sdf = new SimpleDateFormat(config.getDataDateFormatter());
        StringBuilder row = new StringBuilder();
        row.append(config.getRowDataPrefix());
        row.append(formatString("", config.getResponseRowLength(), config.getRowFillChar(), config));
        row.append(formatString("", config.getResponseCellRowLength(), config.getRowFillChar(), config));
        row.append(formatString("", config.getQuestionRowLength(), config.getRowFillChar(), config));
        row.append(formatString("", config.getQuestionItemRowLength(), config.getRowFillChar(), config));
        row.append(formatString(Objects.toString(questionColumn.getId(),null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(questionColumn.getQid(), null), config.getIdLength(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(questionColumn.getValue(),null), config.getResponseEuidLentgh(), config.getSurveyFillChar(), config));
        row.append(formatString(Objects.toString(questionColumn.getText(),null), config.getResponseEuidLentgh(), config.getSurveyFillChar(), config));
        row.append(formatString(questionColumn.getCreateTime() == null ? null : sdf.format(questionColumn.getCreateTime()), config.getCreateTimeLength(), config.getSurveyFillChar(), config));
        row.append(formatString(questionColumn.getModifyTime() == null ? null : sdf.format(questionColumn.getModifyTime()), config.getModifyTimeLength(), config.getSurveyFillChar(), config));
        return formatString(row.toString(), config.getRowLength(), config.getRowFillChar(), config);
    }

    private List<SimpleExportResponse> findAllSimpleResponse(Pageable page, LocalDate triggerDate, LocalDate endDate) {
        String sql = String
                .format("select id,s_id as sid,euid,create_time as createTime,connector_method as collectorMethod,"
                                + "finish_time as finishTime,status,parameters from survey_response where modify_time between '%s' and '%s' limit %s offset %s",
                        triggerDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        page.getPageSize(),
                        page.getOffset());
        log.info("find response sql: {}", sql);
        return nativeSqlHelper.queryListObject(sql, SimpleExportResponse.class);
    }

    private List<SimpleExportResponseCell> findAllSimpleResponseCell(IFFSurveyResponseDataExportConfig config, Pageable page, LocalDate triggerDate, LocalDate endDate) {
        String sql = String
                .format("select id,s_id as sid,r_id as rid,q_id as qid,type,i_val as iVal,cast(d_val as decimal(%d,%d)) as dVal,s_val as sVal,"
                                + "j_val as jVal,comment_val as commentVal,t_val as tVal,create_time as createTime,"
                                + "modify_time as modifyTime,tags from survey_response_cell where modify_time between '%s' and '%s' limit %s offset %s",
                        config.getDecimalPrecision(),
                        config.getDecimalScale(),
                        triggerDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        page.getPageSize(),
                        page.getOffset());
        log.info("find response cell sql: {}", sql);
        return nativeSqlHelper.queryListObject(sql, SimpleExportResponseCell.class);
    }

    private List<SimpleExportQuestion> findAllSimpleQuestion(Pageable page, LocalDate triggerDate, LocalDate endDate) {
        String sql = String
                .format("select id,s_id as sid,name,title, type, create_time as createTime, modify_time as modifyTime from survey_question where modify_time between '%s' and '%s' limit %s offset %s",
                        triggerDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        page.getPageSize(),
                        page.getOffset());
        log.info("find question sql: {}", sql);
        return nativeSqlHelper.queryListObject(sql, SimpleExportQuestion.class);
    }

    private List<SimpleExportQuestionItem> findAllSimpleQuestionItem(Pageable page, LocalDate triggerDate, LocalDate endDate) {
        String sql = String
                .format("select id,q_id as qid, value, text, create_time as createTime, modify_time as modifyTime from survey_question_item where modify_time between '%s' and '%s' limit %s offset %s",
                        triggerDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        page.getPageSize(),
                        page.getOffset());
        log.info("find question item sql: {}", sql);
        return nativeSqlHelper.queryListObject(sql, SimpleExportQuestionItem.class);
    }

    private List<SimpleExportQuestionColumn> findAllSimpleQuestionColumn(Pageable page, LocalDate triggerDate, LocalDate endDate) {
        String sql = String
                .format("select id,q_id as qid, value, text, create_time as createTime, modify_time as modifyTime from survey_question_column where modify_time between '%s' and '%s' limit %s offset %s",
                        triggerDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        page.getPageSize(),
                        page.getOffset());
        log.info("find question column sql: {}", sql);
        return nativeSqlHelper.queryListObject(sql, SimpleExportQuestionColumn.class);
    }

    @Override
    public void updateDataTaskLogStatus(DataTaskLog dataTaskLog, DataTaskLogStatus status) {
        dataTaskLog.setStatus(status);
        dataTaskLogRepository.save(dataTaskLog);
    }
}
