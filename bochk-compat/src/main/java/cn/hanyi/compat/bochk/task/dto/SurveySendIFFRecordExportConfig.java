package cn.hanyi.compat.bochk.task.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SurveySendIFFRecordExportConfig extends DataExportConfig{

    /** 文件内容
     *
     */
    private String firstRowDataPrefix = "000784CLUEM00003SURVEY_INBOX_MSG.TXT";
    private String endDataPrefix = "2CLUEM00002";

    private String dataDateFormatter = "yyyy-MM-dd";
    private int recordEuidLentgh = 40;
    private int recordSendTimeLentgh = 10;
    private int recordSurveyUrlLength = 100;
    private int recordMsgSubjectLength = 100;
    private int recordMsgContentLength = 500;
    private int recordMsgButtonLength = 32;

    /**
     * 文件信息
     */
    private String fileName = "SURVEY_INBOX_MSG.TXT";
    /**
     * 每一行长度（包含行起始字符）
     */
    private int rowLength = 783;

    private int recordRowLength = 783;


    private String iffId = "CLUEM00002";
    private String datasetName = "SURVEY_INBOX_MSG.TXT";

    private int minusDays = 1;
    private int plusDays = 1;
}
