package cn.hanyi.compat.bochk.task.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class IFFSurveyDataExportConfig extends DataExportConfig{

    /** 文件内容
     *
     */
    private int surveyTitleLength = 100;
    private int surveyCodeLength = 20;
    private int sendManageTitleLength = 100;
    private int sendManageSendTokenLength = 20;

    private String rowFillChar = " ";
    private String rowDataPrefix = "1";
    private String endDataPrefix = "2CLUEM00001";
    private String rowEndFillChar = "";
    private String sendManageFillChar = " ";
    /**
     * 文件信息
     */
    private String fileName = "SURVEY_ENABLE.TXT";
    private String fileLocalPath = "/tmp/files/";

    private String firstRowDataPrefix = "000143CLUEM00001SURVEY_ENABLE.TXT";
    private String endRowDataPrefix = "2CLUEM00001";
    private int rowLength = 142;

    private String iffId = "CLUEM00001";
    private String datasetName = "SURVEY_ENABLE.TXT";
}
