package cn.hanyi.compat.bochk.task.entity;

import cn.hanyi.compat.bochk.task.consts.DataTaskLogStatus;
import cn.hanyi.compat.bochk.task.consts.DataTaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;

@Entity
@Getter
@Setter
@Table(name = "data_task_log")
public class DataTaskLog extends BaseEntity {

    @Column(name = "task_id")
    @Schema(description = "任务id")
    private Long taskId;

    @Column(name = "i_date")
    @Schema(description = "任务开始时间")
    private String iDate;

    @Column(name = "type")
    @Schema(description = "任务类型")
    @Enumerated(EnumType.STRING)
    private DataTaskType type;

    @Column(name = "status")
    @Schema(description = "任务状态")
    private DataTaskLogStatus status;

    @Column(name = "fail_msg")
    @Schema(description = "失败原因")
    private String failMsg;

}



