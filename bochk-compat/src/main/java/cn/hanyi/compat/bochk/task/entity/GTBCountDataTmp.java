package cn.hanyi.compat.bochk.task.entity;

import cn.hanyi.compat.bochk.task.consts.DataTXCodeType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
@Table(name = "gtb_count_data_tmp")
@Entity
public class GTBCountDataTmp extends BaseEntity {
    @Size(max = 24, message = "外部客户id长度超多限制")
    @Schema(description = "外部客户id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "cbs_account")
    private String cbsAccount;

    @Schema(description = "外部客户id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "tx_code_type")
    private DataTXCodeType txCodeType;

    @Schema(description = "导入日期")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "import_date")
    private Date importDate;
}
