package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.entity.SurveySendIFFRecord;
import cn.hanyi.compat.bochk.task.repository.SurveySendIFFRepository;
import java.sql.Date;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@ConditionalOnProperty(name = "project-compat.bochk.data-task.enable", havingValue = "true")
public class SendIFFRecordService {
    @Autowired
    private SurveySendIFFRepository surveySendIFFRepository;

    public void saveSurveySend(Long sendRecordId, String body) {
        if (body == null) {
            log.info("skip save survey send iff record");
            return;
        }
        try {
            SurveySendIFFRecord record = JsonHelper.toObject(body, SurveySendIFFRecord.class);
            if (record == null) {
                log.warn("send iff record, 解析发送数据失败: {}", body);
                return;
            }
            record.setSendTime(Date.valueOf(LocalDate.now()));
            record.setSendRecordId(sendRecordId);
            surveySendIFFRepository.save(record);
            log.info("保存IFF发送记录成功，recordId: {}, body:{}", sendRecordId, body);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("保存IFF发送记录失败, recordId: {}, body: {}", sendRecordId, body);
        }
    }


}
