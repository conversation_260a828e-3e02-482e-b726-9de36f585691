package cn.hanyi.compat.bochk.task.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class IFFSurveyResponseDataExportConfig extends DataExportConfig{

    /** 文件内容
     *
     */
    private String rowFillChar = " ";
    private String endDataPrefix = "2CLUEM00002";
    private int wideCharacterLength = 3;
    private char wideCharacterLeftBound = 0x4e00;
    private char wideCharacterRightBound = 0x9fff;
    /**
     * 文件信息
     */
    private String fileName = "SURVEY_RESULT.TXT";
    private String fileLocalPath = "/tmp/files/";
    private int fileRetentionPeriod = 3;


    private String firstRowDataPrefix = "002452CLUEM00002SURVEY_RESULT.TXT";
    private String endRowDataPrefix = "2CLUEM00002";
    private int rowLength = 2451;

    private String dataDateFormatter = "yyyy-MM-dd HH:mm:ss";
    private List<String> parameterKeys = new ArrayList<>();

    private int responseRowLength = 1143;
    private int responseCellRowLength = 675;
    private int questionRowLength = 212;
    private int questionItemRowLength = 210;
    private int questionColumnRowLength = 210;


    private int idLength = 20;
    private int responseEuidLentgh = 40;
    private int createTimeLength = 30;
    private int modifyTimeLength = 30;
    private int responseFinishTimeLength = 30;
    private int responseCollectorMethodLength = 2;
    private int responseStatusLength = 1;
    private int responseParameterLength = 50;

    private int responseCellTypeLength = 2;
    private int responseCellIValLength = 2;
    private int responseCellDValLength = 11;
    private int responseCellSValLength = 100;
    private int responseCellJValLength = 100;
    private int responseCellCommentValLength = 200;
    private int responseCellTValLength = 20;
    private int responseCellTagLength = 100;
    private int responseCellDValPrecision = 6;
    private int responseCellDValScale = 4;

    private int questionNameLength = 10;
    private int questionTitleLength = 100;
    private int questionTypeLength = 2;
    private int questionItemValueLength = 10;
    private int questionItemTextLength = 100;
    private int questionColumnValueLength = 10;
    private int questionColumnTextLength = 100;

    private String iffId = "CLUEM00002";
    private String datasetName = "SURVEY_RESULT.TXT";

    private int decimalPrecision = 10;
    private int decimalScale = 4;
    private int minusDays = 1;
    private int plusDays = 1;
}
