package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.property.GtbBlackListProperty;
import cn.hanyi.compat.bochk.task.repository.GtbBlackListDataRepository;
import cn.hanyi.survey.compat.user.entity.BaseExternalUser;
import cn.hanyi.survey.compat.user.service.IExternalUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class GtbBlackListDataService implements IExternalUserService {
    @Autowired
    private GtbBlackListDataRepository gtbBlackListDataRepository;
    @Autowired
    private GtbBlackListProperty gtbBlackListProperty;


    @Override
    public <T extends BaseExternalUser> Boolean isBlack(T user) {

        if (!gtbBlackListProperty.getBlackListEnable()) {
            // 黑名单功能未开启
            return false;
        }

        if (user == null || StringUtils.isEmpty(user.getExternalUserId())) {
            // 用户为空或者用户ID为空
            return false;
        }

        String cbsAccount = user.getExternalUserId();
        log.info("check externalUserId:{} cbsAccount:{}", user.getExternalUserId(), cbsAccount);
        Boolean isBlack = Optional.ofNullable(gtbBlackListDataRepository.findTopByExternalUserIdOrderByImportDateDesc(cbsAccount))
                .map(gtbBlackListData -> {
                    try {
                        String status = gtbBlackListData.getStatus();
                        List<String> statusList = gtbBlackListProperty.getBlackListStatus();
                        List<String> blackListBranchNo = gtbBlackListProperty.getBlackListBranchNo();
                        String branchNo = gtbBlackListData.getBranchNo();
                        String deptCode = gtbBlackListData.getDeptCode();
                        String guarInd = gtbBlackListData.getGuarInd();
                        log.info("cbsAccount:{}, status: {}, branchNo:{} deptCode:{} guarInd:{}", cbsAccount, status, branchNo, deptCode, guarInd);
                        // status 为空也在黑名单中
                        return StringUtils.isEmpty(status) || statusList.contains(status) || blackListBranchNo.contains(branchNo) || blackGuarantor(deptCode, guarInd);

                    } catch (Exception e) {
                        log.error("gtbBlackListDataRepository.findTopByExternalUserIdOrderByImportDateDesc error", e);
                        return false;
                    }
                })
                .orElse(false);

        log.info("check user:{} in black list: {}", cbsAccount, isBlack);
        return isBlack;
    }

    @Override
    public BaseExternalUser findByExternalUserId(String externalUserId) {
        return gtbBlackListDataRepository.findTopByExternalUserIdOrderByImportDateDesc(convertCbsAccount(externalUserId));
    }

    private String convertCbsAccount(String externalUserId) {
        String[] id = externalUserId.split(gtbBlackListProperty.getBlackLisUserSplit());
        log.info("convert externalUserId:{} to gtb blacklist cbsAccount:{}", externalUserId, id[0]);
        return id[0];
    }

    private Boolean blackGuarantor(String deptCode, String guarInd) {

        boolean isBlackCode = gtbBlackListProperty.getBlackListDeptCode().contains(deptCode);
        boolean isGuarInd = !StringUtils.isEmpty(guarInd) && !Objects.equals(guarInd, gtbBlackListProperty.getBlackLisGuarInd());

        return isBlackCode || isGuarInd;
    }
}
