package cn.hanyi.compat.bochk.task.repository;

import cn.hanyi.compat.bochk.task.entity.DataTask;
import cn.hanyi.compat.bochk.task.consts.DataTaskType;
import java.util.List;
import java.util.Optional;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface DataTaskRepository extends ResourceRepository<DataTask, Long> {
    List<DataTask> findAllByStatusNot(int status);
    List<DataTask> findAllByStatus(int status);
    Optional<DataTask> findByIdAndStatus(Long id, int status);
    List<DataTask> findAllByTaskTypeAndStatus(DataTaskType taskType,int status);
}
