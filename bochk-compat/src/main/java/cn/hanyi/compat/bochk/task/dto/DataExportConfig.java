package cn.hanyi.compat.bochk.task.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DataExportConfig extends SFTPConnectConfigDto{

    /**
     * 紧急联系人手机号
     */
    private String contactMobile;
    private String contactContent = "【cem系统】--Ods数据导出任务--上传文件失败，请及时查看！";
    private String version = "v_1.0";
    private int pageLimit = 500;
    /** 文件内容
     *
     */

    private String surveyFillChar = " ";
    private String firstRowDataPrefix = "";
    private int rowLength = 142;
    private String rowFillChar = " ";
    private String rowDataPrefix = "1";
    private String endDataPrefix = "2CLUEM00001";
    private String endRowDateFormatter = "yyyy/MM/dd";
    private String endRowtimeFormatter = "yyyy/MM/ddHHmmss";
    private Long maxDataCount = 999999999L;
    private String rowEndFillChar = "";
    private int wideCharacterLength = 3;
    private char wideCharacterLeftBound = 0x4e00;
    private char wideCharacterRightBound = 0x9fff;
    /**
     * 文件信息
     */
    private String fileName = "SURVEY_ENABLE.TXT";
    private String fileLocalPath = "/tmp/files/";
    private int fileRetentionPeriod = 3;
    /**
     * sftp连接信息
     **/
    private boolean needConnect = true;
    private String sftpHost;
    private Integer sftpPort;
    private String sftpUser = "root";
    private String sftpPassword = "";
    private List<String> remoteDir = new ArrayList<>();
    private long retryInterval = 10000;
    private String privateKeyPath = "/tmp/files/";
    /**
     * notify信息
     */
    private DataExportNotifyConfig notifyConfig = new DataExportNotifyConfig();
    private String acDateFormatter = "yyyyMMdd";
    private String caCertPath = "/tmp/bochk-ca.cer";
    private String apiUrl;
    private Boolean enableSSL = true;
    private String iffId = "CLUEM00001";
    private String datasetName = "SURVEY_ENABLE.TXT";
    private String srcApp = "CLUEM";
    private String channelCode = "000001";
    private String callCode = "VHKUEM0000000";
}
