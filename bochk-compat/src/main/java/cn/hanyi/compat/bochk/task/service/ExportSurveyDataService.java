package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.consts.DataTaskLogStatus;
import cn.hanyi.compat.bochk.task.consts.DataTaskType;
import cn.hanyi.compat.bochk.task.dto.DataTaskExecuteDto;
import cn.hanyi.compat.bochk.task.dto.IFFSurveyDataExportConfig;
import cn.hanyi.compat.bochk.task.entity.DataTask;
import cn.hanyi.compat.bochk.task.entity.DataTaskLog;
import cn.hanyi.compat.bochk.task.repository.DataTaskLogRepository;
import cn.hanyi.compat.bochk.task.repository.DataTaskRepository;
import cn.hanyi.compat.bochk.task.service.impls.IFFExportDataService;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.repository.SurveyRepository;
import java.io.BufferedWriter;
import java.io.IOException;
import java.net.http.HttpResponse;
import java.nio.file.Path;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.shade.io.netty.handler.codec.http.HttpResponseStatus;
import org.befun.core.utils.JsonHelper;
import org.befun.task.annotation.TaskLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@EnableAsync
@Slf4j
@ConditionalOnProperty(name = "project-compat.bochk.data-task.enable", havingValue = "true")
public class ExportSurveyDataService extends SftpService implements
        IFFExportDataService<IFFSurveyDataExportConfig> {

    @Autowired
    private DataTaskRepository dataTaskRepository;

    @Autowired
    private DataTaskLogRepository dataTaskLogRepository;

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private SendManageRepository sendManageRepository;

    @Autowired
    private NotifyService notifyService;

    @Async
    @TaskLock(key = "bochk-data-task")
    public void executeTasks(Long taskId) {
        if (taskId != null) {
            log.info("execute task, taskId: {}",taskId);
            Optional<DataTask> taskOptional = dataTaskRepository.findByIdAndStatus(taskId, 1);
            taskOptional.ifPresent(t -> executeTaskAsync(new DataTaskExecuteDto(t, null, null, true, true)));
        } else {
            List<DataTask> tasks = dataTaskRepository.findAllByTaskTypeAndStatus(DataTaskType.EXPORT_DATA_TASK, 1);
            log.info("find {} tasks", tasks.size());
            for (DataTask task : tasks) {
                executeTaskAsync(new DataTaskExecuteDto(task, null, null, true, true));
            }
        }
    }

    // 定时任务：每分钟查询一次
    @Scheduled(cron = "${project-compat.bochk.data-task.scheduler.cron:0 0/1 * * * ?}")
    @Async
    @TaskLock(key = "bochk-data-task")
    public void checkAndExecuteTasks() {
        List<DataTask> tasks = dataTaskRepository.findAllByTaskTypeAndStatus(DataTaskType.EXPORT_DATA_TASK, 1);

        for (DataTask task : tasks) {
            executeTaskAsync(new DataTaskExecuteDto(task, null, null, true, true));
        }
    }

    @Override
    public void run(DataTaskExecuteDto dto, DataTaskLog taskLog) {
        DataTask task = dto.getTask();
        log.info("----------- start execute task: {} -----------", task.getConfig());
        try {
            LocalDate exportDate = LocalDate.now().minusDays(1);
            if (dto.getTriggerDate() != null) {
                exportDate = dto.getTriggerDate();
            }
            IFFSurveyDataExportConfig config = JsonHelper
                    .toObject(task.getConfig(), IFFSurveyDataExportConfig.class);
            Path tempFile = exportData(config, exportDate, dto.getEndDate());
            if (!dto.getDoNotUpload()) {
                uploadFile(tempFile, config);
            }
            if (!dto.getDoNotNotify()) {
                HttpResponse<String> response = notifyService.sendNotify(config, exportDate);
                if (response.statusCode() == HttpResponseStatus.OK.code()) {
                    taskLog.setStatus(DataTaskLogStatus.SUCCESSED);
                } else {
                    taskLog.setStatus(DataTaskLogStatus.FAILED);
                }
                taskLog.setFailMsg(String.valueOf(response.statusCode()));
                dataTaskLogRepository.save(taskLog);
            }

        } catch (Exception e) {
            // 异常日志记录
            log.error("execute task error: caused by: {}", e.getMessage());
            e.printStackTrace();
            taskLog.setStatus(DataTaskLogStatus.FAILED);
            taskLog.setFailMsg(e.getMessage());
            dataTaskLogRepository.save(taskLog);
        }
        log.info("---------- execute task end: {} ----------", task.getTaskType());
    }

    @Override
    public Long writeTableData(IFFSurveyDataExportConfig config, BufferedWriter writer, LocalDate triggerDate, LocalDate endDate) throws IOException {
        Long surveyCount = exportPaginatedData(config, writer, surveyPage -> surveyRepository.findAll(PageRequest.of(surveyPage, config.getPageLimit())));
        Long sendManageCount = exportPaginatedData(config, writer, sendManagePage -> sendManageRepository.findAll(PageRequest.of(sendManagePage, config.getPageLimit())));
        return surveyCount + sendManageCount;
    }

    private Long exportPaginatedData(IFFSurveyDataExportConfig config, BufferedWriter writer, Function<Integer, Page<?>> fetchPageData) throws IOException {
        int page = 0;
        int limit = config.getPageLimit();
        boolean hasNext = true;
        Long count = 0L;

        while (hasNext) {
            Page<?> pageData = fetchPageData.apply(page);

            for (Object data : pageData.getContent()) {
                String rowData = buildRowData(config, data);
                writer.write(rowData);
                count += 1;
                writer.newLine();
            }

            hasNext = pageData.getContent().size() >= limit;
            page++;
        }
        return count;
    }

    @Override
    public String buildRowData(IFFSurveyDataExportConfig config, Object dto) {
        if (dto instanceof Survey) {
            return buildSurveyRowData(config, (Survey) dto);
        } else if (dto instanceof SendManage) {
            return buildSendManageRowData(config, (SendManage) dto);
        }
        throw new IllegalArgumentException("Unsupported DTO type");
    }

    private String buildSurveyRowData(IFFSurveyDataExportConfig config, Survey surveyDto) {
        StringBuilder row = new StringBuilder();
        row.append(config.getRowDataPrefix());
        row.append(formatString(surveyDto.getTitle(), config.getSurveyTitleLength(), config.getSurveyFillChar(), config));
        row.append(formatString(surveyDto.getSurveyCode(), config.getSurveyCodeLength(), config.getSurveyFillChar(), config));
        row.append(config.getSendManageFillChar().repeat(config.getSendManageSendTokenLength()));
        row.append(surveyDto.getStatus() == SurveyStatus.COLLECTING ? 1 : 0);
        row.append(config.getRowEndFillChar());
        return row.toString();
    }

    private String buildSendManageRowData(IFFSurveyDataExportConfig config, SendManage sendManageDto) {
        StringBuilder row = new StringBuilder();
        row.append(config.getRowDataPrefix());
        row.append(formatString(sendManageDto.getTitle(), config.getSendManageTitleLength(), config.getSendManageFillChar(), config));
        row.append(config.getSurveyFillChar().repeat(config.getSurveyCodeLength()));
        row.append(formatString(sendManageDto.getSendToken(), config.getSendManageSendTokenLength(), config.getSendManageFillChar(), config));
        row.append(sendManageDto.getEnable() ? 1 : 0);
        row.append(config.getRowEndFillChar());
        return row.toString();
    }

    @Override
    public void updateDataTaskLogStatus(DataTaskLog dataTaskLog, DataTaskLogStatus status) {
        dataTaskLog.setStatus(status);
        dataTaskLogRepository.save(dataTaskLog);
    }
}
