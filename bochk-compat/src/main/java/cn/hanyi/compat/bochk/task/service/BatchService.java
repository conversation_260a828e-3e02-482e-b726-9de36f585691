package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.entity.GTBCountDataTmp;
import cn.hanyi.compat.bochk.task.entity.GtbBlackListData;
import cn.hanyi.compat.bochk.task.repository.GTBCountDataTmpRepository;
import cn.hanyi.compat.bochk.task.repository.GTBCustomerInfoTmpRepository;
import cn.hanyi.compat.bochk.task.repository.GtbBlackListDataRepository;
import com.boc.mcf.reader.data.ReadableData;
import com.boc.mcf.reader.util.ReaderUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.FileInputStream;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class BatchService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private GtbBlackListDataRepository gtbBlackListDataRepository;

    @Autowired
    private GTBCustomerInfoTmpRepository gtbCustomerInfoTmpRepository;

    @Autowired
    private GTBCountDataTmpRepository gtbCountDataTmpRepository;


    @SneakyThrows
    public static void main(String[] args) {
        FileInputStream stream = new FileInputStream("USMK.ISB.DLD.CWM.DWH.DW56CIF");
        ReadableData data = ReaderUtils.read("DW56CIF", stream);
        stream.close();
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsertGTBBlackListData(List<GtbBlackListData> dataList, int batchSize) {
        for (int i = 0; i < dataList.size(); i++) {
            GtbBlackListData data = dataList.get(i);
            if (data.getId() == null) {
                entityManager.persist(dataList.get(i));
            } else {
                entityManager.merge(data);
            }

            if (i > 0 && i % batchSize == 0) {
                entityManager.flush();
                entityManager.clear();
            }
        }

        // 刷新并清空缓存
        entityManager.flush();
        entityManager.clear();
    }

    public void deleteOldGTBBlackListTmpData(Integer dataTmpStorageDays, int batchSize) {
        Date cutoffDate = Date.from(LocalDate.now().minusDays(dataTmpStorageDays)
                .atStartOfDay(ZoneId.systemDefault()).toInstant());

        boolean hasMoreRecords = true;

        log.info("开始删除历史数据");
        int total = 0;

        while (hasMoreRecords) {
            // 每个批次在独立事务中执行
            Integer deletedRows = transactionTemplate.execute(status ->
                    gtbBlackListDataRepository.deleteBatchByImportDateBefore(cutoffDate, batchSize)
            );

            if (deletedRows == null || deletedRows == 0) {
                hasMoreRecords = false;
            } else {
                total += deletedRows;
                log.info("删除历史数据： {} ", total);
            }

            // 小延迟，减轻数据库压力
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        log.info("删除历史数据完成:{}", total);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsertGTBCountDataTmp(List<GTBCountDataTmp> dataList, int batchSize) {
        for (int i = 0; i < dataList.size(); i++) {
            GTBCountDataTmp data = dataList.get(i);
            if (data.getId() == null) {
                entityManager.persist(dataList.get(i));
            } else {
                entityManager.merge(data);
            }
            if (i > 0 && i % batchSize == 0) {
                entityManager.flush();
                entityManager.clear();
            }
        }

        // 刷新并清空缓存
        entityManager.flush();
        entityManager.clear();
    }

    public void deleteOldIFFCountTmpData(Integer dataTmpStorageDays, int batchSize) {
        Date cutoffDate = Date.from(LocalDate.now().minusDays(dataTmpStorageDays)
                .atStartOfDay(ZoneId.systemDefault()).toInstant());

        boolean hasMoreRecords = true;

        log.info("开始删除历史数据");

        int total = 0;
        while (hasMoreRecords) {
            // 每个批次在独立事务中执行
            Integer deletedRows = transactionTemplate.execute(status ->
                    gtbCountDataTmpRepository.deleteAllByImportDateBefore(cutoffDate, batchSize)
            );

            if (deletedRows == null || deletedRows == 0) {
                hasMoreRecords = false;
            } else {
                total += deletedRows;
                log.info("删除历史数据： {} ", total);
            }

            // 小延迟，减轻数据库压力
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        log.info("删除历史数据完成:{}", total);
    }

    public void deleteCustomerInfoTmpData(Integer dataTmpStorageDays, int batchSize) {
        Date cutoffDate = Date.from(LocalDate.now().minusDays(dataTmpStorageDays)
                .atStartOfDay(ZoneId.systemDefault()).toInstant());

        boolean hasMoreRecords = true;

        log.info("开始删除历史数据");
        int total = 0;

        while (hasMoreRecords) {
            // 每个批次在独立事务中执行
            Integer deletedRows = transactionTemplate.execute(status ->
                    gtbCustomerInfoTmpRepository.deleteBatchByImportDateBefore(cutoffDate, batchSize)
            );

            if (deletedRows == null || deletedRows == 0) {
                hasMoreRecords = false;
            } else {
                total += deletedRows;
                log.info("删除历史数据： {} ", total);
            }

            // 小延迟，减轻数据库压力
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        log.info("删除历史数据完成:{}", total);
    }
}
