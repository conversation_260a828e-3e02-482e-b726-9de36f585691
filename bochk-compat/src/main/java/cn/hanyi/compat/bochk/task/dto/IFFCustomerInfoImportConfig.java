package cn.hanyi.compat.bochk.task.dto;

import cn.hanyi.compat.bochk.task.consts.CustomerInfoFileType;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class IFFCustomerInfoImportConfig extends DataImportConfig {
    private List<CustomerInfoFileType> importFileType = List.of(CustomerInfoFileType.BDP_CBS, CustomerInfoFileType.BDP_COMPANY, CustomerInfoFileType.AM_USR, CustomerInfoFileType.BDP_PROFILE, CustomerInfoFileType.DW56CIF);

    private String fileBdpCbsPrefix = "GTBBDPCBSAIF";
    private String fileBdpCbsCharset = "utf-8";
    private Integer bdpRecordLength = 1644;
    private Integer bdpAcNoLeftIndex = 1;
    private Integer bdpAcNoRightIndex = 15;
    private Integer bdpCinNoLeftIndex = 15;
    private Integer bdpCinNoRightIndex = 26;
    private Integer bdpBankCodeLeftIndex = 1;
    private Integer bdpBankCodeRightIndex = 4;

    private String fileAmUsrPrefix = "GTB_AM_USRIF";
    private String fileAmUsrCharset = "utf-8";
    private Integer amusrRecordLength = 2006;
    private Integer amusrAcNoLeftIndex = 1;
    private Integer amusrAcNoRightIndex = 15;
    private Integer amusrUsrIdLeftIndex = 15;
    private Integer amusrUsrIdRightIndex = 21;
    private Integer amusrStatPrimaryLeftIndex = 149;
    private Integer amusrStatPrimaryRightIndex = 150;
    private Map<String, String> amusrStatPrimaryMap = Map.of("Y","PU","N","DU","C","CU");

    private String fileBdpCompanyPrefix = "GTBBDPCOMPANYAIF";
    private String fileBdpCompanyCharset = "utf-8";
    private Integer compRecordLength = 1014;
    private Integer compAcNoLeftIndex = 1;
    private Integer compAcNoRightIndex = 15;
    private Integer compCinNoLeftIndex = 23;
    private Integer compCinNoRightIndex = 34;
    private Integer compSegmenCodeLeftIndex = 1000;
    private Integer compSegmenCodeRightIndex = 1003;

    private String fileBdpProfilePrefix = "GTBBDPPROFILE";
    private String fileBdpProfileCharset = "utf-8";
    private Integer profileRecordLength = 141;
    private Integer profileAcNoLeftIndex = 1;
    private Integer profileAcNoRightIndex = 15;
    private Integer profileUserIdLeftIndex = 15;
    private Integer profileUserIdRightIndex = 21;
    private Integer profileModuleIdLeftIndex = 21;
    private Integer profileModuleIdRightIndex = 31;
    private Map<String, String> profileModuleIdMap = Map.of("GtbMdRGPAY","支付","GtbMdGTBTRD","環球貿易","GtbMdLFUNC","本地特色服務","GtbMdGNSNE","信息管理及一般設定","GtbMdLQMGT","流動性管理","GtbMdRCCLN","收款","GtbMdTSMGT","財資","GtbMdCNFIS","ERP云服務");

    private String fileDwCifPrefix = "USMK.ISB.DLD.CWM.DWH.DW56CIF";
    private String fileDwCifCharset = "ibm-1047";
    private Integer dwcifRecordLength = 25605;
    private Integer dwcifCinNoLeftIndex = 12;
    private Integer dwcifCinNoRightIndex = 23;
    private Integer dwcifCustomerManagerIdLeftIndex = 2176;
    private Integer dwcifCustomerManagerIdRightIndex = 2184;

}
