package cn.hanyi.compat.bochk.task.consts;

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum DataTXCodeType {

    Z100("GIB登录次数"),
    Z103("GMB登录次数"),
    P100("转账交易次数"),
    P140("電匯交易次数"),
    P160("中銀快匯交易次数"),
    P280("FPS交易次数"),
    T180("小企-兌換貨幣交易次数"),
    T110("定期交易次数"),
    P262("手機入票交易次数"),
    Z170("eVer交易次数"),
    Z130("eCon交易次数");

    private final String description;
    private static final Map<String, DataTXCodeType> BY_NAME = new HashMap<>();
    private static final DataTXCodeType[] BY_ORDINAL = values(); // 缓存所有枚举常量


    static {
        for (DataTXCodeType type : values()) {
            BY_NAME.put(type.name(), type);
        }
    }

    DataTXCodeType(String description) {
        this.description = description;
    }

    public static boolean contains(String value) {
        return BY_NAME.containsKey(value);
    }

    public static DataTXCodeType fromOrdinal(int ordinal) {
        if (ordinal < 0 || ordinal >= BY_ORDINAL.length) {
            throw new IllegalArgumentException("Invalid ordinal: " + ordinal);
        }
        return BY_ORDINAL[ordinal];
    }
}
