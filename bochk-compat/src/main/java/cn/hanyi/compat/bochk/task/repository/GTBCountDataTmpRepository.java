package cn.hanyi.compat.bochk.task.repository;

import cn.hanyi.compat.bochk.task.entity.GTBCountDataTmp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface GTBCountDataTmpRepository extends ResourceRepository<GTBCountDataTmp, Long> {
    @Modifying
    @Query(value = "DELETE FROM gtb_count_data_tmp WHERE import_date < ?1 LIMIT ?2", nativeQuery = true)
    int deleteAllByImportDateBefore(Date importDate, int batchSize);
    List<GTBCountDataTmp> findAllByImportDateAndIdGreaterThan(String date, Long id, Pageable pageable);

    @Query(nativeQuery = true, value = "select cbs_account as cbsAccount, tx_code_type as txCodeType, count(1) as countRes from gtb_count_data_tmp where import_date  = ?1 group by cbs_account, tx_code_type")
    List<Map<String, Object>> countByImportDate(Date importDate);
}
