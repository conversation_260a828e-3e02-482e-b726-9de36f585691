package cn.hanyi.compat.bochk.task.property;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "project-compat.bochk.data-task")
@Getter
@Setter
public class DataTaskProperty {
    private String cacertPath;
    private String apiUrl;
    private String apiKey = "";

    /**
     * 问卷渠道-自定义网关推送地址
     */
    private Boolean enableLocalConnector = false;
    private String connectorUrl = "https://bochk-send-connector";
}
