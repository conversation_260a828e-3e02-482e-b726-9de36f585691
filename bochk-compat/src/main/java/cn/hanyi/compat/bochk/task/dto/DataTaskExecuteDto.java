package cn.hanyi.compat.bochk.task.dto;

import cn.hanyi.compat.bochk.task.entity.DataTask;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DataTaskExecuteDto {
    private DataTask task;
    private LocalDate triggerDate;
    private LocalDate endDate;
    private Boolean doNotUpload = false;
    private Boolean doNotNotify = false;
}
