package cn.hanyi.compat.bochk.task.service;

import cn.hanyi.compat.bochk.task.dto.DataTaskExecuteDto;
import cn.hanyi.compat.bochk.task.entity.DataTask;
import cn.hanyi.compat.bochk.task.repository.DataTaskRepository;
import cn.hanyi.compat.bochk.task.consts.DataTaskType;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.befun.task.annotation.TaskLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@ConditionalOnProperty(name = "project-compat.bochk.data-task.enable", havingValue = "true")
public class DataTaskService {

    @Autowired
    private DataTaskRepository dataTaskRepository;

    @Autowired
    private ExportSurveyDataService exportSurveyDataService;

    @Autowired
    private ImportIFFDataServiceImpl importIFFDataService;

    @Autowired
    private ImportBlackListDataServiceImpl importBlackListDataService;

    @Autowired
    private ExportSurveyResponseDataService exportSurveyResponseDataService;

    @Autowired
    private ExportSurveySendIFFRecordService exportSurveySendIFFRecordService;
    @Autowired
    private OldImportCustomerInfoServiceImpl oldImportCustomerInfoService;
    @Autowired
    private ImportCustomerInfoService importCustomerInfoService;

    private final Map<DataTaskType, Consumer<DataTaskExecuteDto>> taskHandlers = new EnumMap<>(DataTaskType.class);


    @PostConstruct
    public void init() {
        taskHandlers.put(DataTaskType.EXPORT_DATA_TASK, exportSurveyDataService::executeTaskAsync);
        taskHandlers.put(DataTaskType.IMPORT_GTBCOUNT_DATA_TASK, importIFFDataService::executeTaskAsync);
        taskHandlers.put(DataTaskType.IMPORT_BLACKLIST_DATA_TASK, importBlackListDataService::executeTaskAsync);
        taskHandlers.put(DataTaskType.EXPORT_SURVEY_RESPONSE_DATA_TASK, exportSurveyResponseDataService::executeTaskAsync);
        taskHandlers.put(DataTaskType.EXPORT_SURVEY_SEND_DATA_TASK, exportSurveySendIFFRecordService::executeTaskAsync);
        taskHandlers.put(DataTaskType.IMPORT_CUSTOMER_INFO_TASK, importCustomerInfoService::executeTaskAsync);
    }

    @Async
    @TaskLock(key = "bochk-data-task")
    public void executeTasks(Long taskId, DataTaskType taskType, Boolean allTask, LocalDate triggerDate, LocalDate endDate, Boolean doNotUpload, Boolean doNotNotify) {
        List<DataTask> tasks = new ArrayList<>();
        if (taskId != null) {
            log.info("execute task, taskId: {}",taskId);
            Optional<DataTask> taskOptional = dataTaskRepository.findByIdAndStatus(taskId, 1);
            taskOptional.ifPresent(tasks::add);
        } else if (taskType != null) {
            tasks = dataTaskRepository.findAllByTaskTypeAndStatus(taskType, 1);
        } else if (allTask != null && allTask) {
            tasks = dataTaskRepository.findAllByStatus(1);
        } else {
            tasks = dataTaskRepository.findAllByTaskTypeAndStatus(DataTaskType.EXPORT_DATA_TASK, 1);
        }
        log.info("find {} tasks", tasks.size());
        for (DataTask task : tasks) {
            DataTaskExecuteDto executeDto = new DataTaskExecuteDto();
            executeDto.setTask(task);
            executeDto.setTriggerDate(triggerDate);
            executeDto.setEndDate(endDate);
            executeDto.setDoNotUpload(doNotUpload != null && doNotUpload);
            executeDto.setDoNotNotify(doNotNotify != null && doNotNotify);
            executeTaskAsync(executeDto);
        }
    }

    private void executeTaskAsync(DataTaskExecuteDto taskExecuteDto) {
        if (taskExecuteDto == null) {
            throw new IllegalArgumentException("Task cannot be null");
        }

        DataTaskType taskType = taskExecuteDto.getTask().getTaskType();
        Consumer<DataTaskExecuteDto> handler = taskHandlers.get(taskType);

        if (handler != null) {
            handler.accept(taskExecuteDto);
        } else {
            log.warn("Task type {} is not supported", taskType);
        }
    }
}
