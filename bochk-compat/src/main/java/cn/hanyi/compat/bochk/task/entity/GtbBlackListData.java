package cn.hanyi.compat.bochk.task.entity;

import cn.hanyi.survey.compat.user.entity.BaseExternalUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.AttributeOverride;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@Table(name = "gtb_blacklist_data")
@AttributeOverride(name = "externalUserId", column = @Column(name = "cbs_account"))
public class GtbBlackListData extends BaseExternalUser {

    @Column(name = "import_data")
    @Schema(description = "原始数据")
    private String importData;

    @Column(name = "branch_no")
    @Schema(description = "机构号")
    private String branchNo;

    @Column(name = "status")
    @Schema(description = "状态")
    private String status;

    @Column(name = "import_date")
    @Schema(description = "导入时间")
    private Date importDate;

    @Column(name = "dept_code")
    @Schema(description = "部门代码")
    private String deptCode;

    @Column(name = "guar_ind")
    @Schema(description = "担保人标识")
    private String guarInd;

    @Column(name = "type")
    @Schema(description = "外部用户类型")
    private String type;
}



