package cn.xmplus.compat.cqrcb;


import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import static cn.xmplus.compat.cqrcb.CqrcbCompatCommonConfig.CQRCB_COMPAT_PREFIX;
import static cn.xmplus.compat.cqrcb.CqrcbCompatDataAccessConfig.SCAN_PACKAGE;

@Configuration
@ComponentScan(SCAN_PACKAGE)
@ConditionalOnProperty(prefix = CQRCB_COMPAT_PREFIX, name = "enabled", havingValue = "true")
public class CqrcbCompatDataAccessConfig {
    static final String SCAN_PACKAGE = "cn.xmplus.compat.cqrcb.dataaccess";
}
