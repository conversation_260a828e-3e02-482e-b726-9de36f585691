package cn.xmplus.compat.cqrcb.dataaccess;

import lombok.Getter;
import lombok.Setter;
import org.befun.task.BaseTaskDetailDto;

import java.util.List;

@Getter
@Setter
public class CqrcbDataHubNextParseTaskDetailDto extends BaseTaskDetailDto {
    private Long orgId;
    private Long dataAccessId;
    private List<Long> dataAccessParamIds;
    private Long cellId;

    public CqrcbDataHubNextParseTaskDetailDto(Long orgId, Long dataAccessId, List<Long> dataAccessParamIds, Long cellId) {
        this.orgId = orgId;
        this.dataAccessId = dataAccessId;
        this.dataAccessParamIds = dataAccessParamIds;
        this.cellId = cellId;
    }
}
