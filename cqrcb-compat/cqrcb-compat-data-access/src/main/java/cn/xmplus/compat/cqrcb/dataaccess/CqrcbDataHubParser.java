package cn.xmplus.compat.cqrcb.dataaccess;

import cn.hanyi.ctm.constant.DataAccessCellStatus;
import cn.hanyi.ctm.dataaccess.datahub.DataHubParser;
import cn.hanyi.ctm.dto.DataHubContextDto;
import cn.hanyi.ctm.dto.DataHubDataDto;
import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessCell;
import cn.hanyi.ctm.entity.DataAccessParams;
import cn.hanyi.ctm.repository.DataAccessCellRepository;
import cn.hanyi.ctm.repository.DataAccessParamsRepository;
import cn.hanyi.ctm.repository.DataAccessRepository;
import cn.xmplus.compat.cqrcb.api.IApiCall;
import cn.xmplus.compat.cqrcb.consts.ApiType;
import cn.xmplus.compat.cqrcb.consts.ProjectCqrcbCompatType;
import cn.xmplus.compat.cqrcb.entity.ProjectCqrcbCompat;
import cn.xmplus.compat.cqrcb.repository.ProjectCqrcbCompatRepository;
import com.aliyun.datahub.client.model.RecordKey;
import com.jayway.jsonpath.Configuration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Primary
@Component
public class CqrcbDataHubParser extends DataHubParser {

    @Autowired
    private DataAccessRepository dataAccessRepository;
    @Autowired
    private DataAccessCellRepository dataAccessCellRepository;
    @Autowired
    private DataAccessParamsRepository dataAccessParamsRepository;
    @Autowired
    private ProjectCqrcbCompatRepository projectCqrcbCompatRepository;
    @Autowired(required = false)
    private CqrcbDataHubNextParseTask cqrcbDataHubNextParseTask;
    @Autowired(required = false)
    private List<IApiCall> apiCalls = new ArrayList<>();
    private final Map<ApiType, IApiCall> apiCallMap = new HashMap<>();
    private static final String NEXT_PARSE_PARAM_IDS = "nextParseParamIds";

    @PostConstruct
    public void init() {
        apiCalls.forEach(i -> apiCallMap.put(i.type(), i));
    }

    @Override
    protected boolean parseJsonData(DataHubContextDto context) {
        Map<String, ApiType> apiParamMap = getApiParamMap(context.getDataAccess());
        List<Long> nextParseParamIds = new ArrayList<>();
        String originData = context.getData().getRawData();
        Object defaultDocument = Configuration.defaultConfiguration().jsonProvider().parse(originData);
        for (DataAccessParams param : context.getDataAccessParams()) {
            // 如果 参数名称 存在于 apiParamMap 中，则添加到第二轮解析
            ApiType type = apiParamMap.get(param.getParamsName());
            if (type != null) {
                nextParseParamIds.add(param.getId());
                continue;
            }
            // 第一轮解析
            boolean success = paramValueIsCheck(context, param, () -> paramValue(context, param, defaultDocument));
            if (!success) {
                // 参数校验失败，直接结束
                return false;
            }
        }
        if (!nextParseParamIds.isEmpty()) {
            context.getExtraData().put(NEXT_PARSE_PARAM_IDS, JsonHelper.toJson(nextParseParamIds));
        }
        return true;
    }

    @Override
    @Transactional
    public void save(DataHubContextDto context) {
        DataAccessCell cell = new DataAccessCell(context.getDataAccess());
        cell.setStatus(DataAccessCellStatus.INIT);
        cell.setMessageId(context.getMessageId());
        cell.setMessageData(context.getData().getRawData());
        cell.setExtraData(JsonHelper.toJson(context.getExtraData()));
        cell.setParsedParams(JsonHelper.toJson(context.getParsedParameters()));
        Object placeholder = context.getExtraData().get(NEXT_PARSE_PARAM_IDS);
        if (placeholder != null) {
            List<Long> nextParseParamIds = JsonHelper.toList(placeholder.toString(), Long.class);
            if (!nextParseParamIds.isEmpty()) {
                save(context, cell);
                if (cqrcbDataHubNextParseTask != null) {
                    // 需要第二步解析,发送到任务队列
                    cqrcbDataHubNextParseTask.performAsync(
                            new CqrcbDataHubNextParseTaskDetailDto(
                                    context.getDataAccess().getOrgId(),
                                    context.getDataAccess().getId(),
                                    nextParseParamIds,
                                    cell.getId()
                            )
                    );
                }
                return;
            }
        }
        // 不需要第二步的解析，直接保存，并发送解析完毕事件
        saveAndTriggerEvent(context, cell);
    }

    @Transactional
    public void parseStep2Params(Long orgId, Long dataAccessId, Long cellId, List<Long> step2ParamIds) {
        log.info("cqrcb data access next parse start : cellId {}", cellId);
        if (orgId == null || dataAccessId == null || cellId == null || cellId <= 0 || CollectionUtils.isEmpty(step2ParamIds)) {
            log.warn("cqrcb data access next parse cancel by require params: orgId {}, dataAccessId {}, cellId {}, step2ParamIds {}", orgId, dataAccessId, cellId, step2ParamIds == null ? null : step2ParamIds.stream().map(Objects::toString).collect(Collectors.joining(",")));
            return;
        }
        DataAccess dataAccess = dataAccessRepository.findById(dataAccessId).orElse(null);
        if (dataAccess == null) {
            log.warn("cqrcb data access next parse cancel by dataAccess is null: cellId {}", cellId);
            return;
        }
        DataAccessCell dataAccessCell = dataAccessCellRepository.findById(cellId).orElse(null);
        if (dataAccessCell == null) {
            log.warn("cqrcb data access next parse cancel by dataAccessCell is null: cellId {}", cellId);
            return;
        }
        List<DataAccessParams> dataAccessParams = dataAccessParamsRepository.findAllById(step2ParamIds);
        if (CollectionUtils.isEmpty(dataAccessParams)) {
            log.warn("cqrcb data access next parse cancel by dataAccessParams is null: cellId {}", cellId);
            return;
        }
        Map<String, ApiType> apiParamMap = getApiParamMap(orgId, dataAccessId);
        if (apiParamMap.isEmpty()) {
            log.warn("cqrcb data access next parse cancel by apiParamMap is empty: cellId {}", cellId);
            return;
        }
        DataHubContextDto mockContext = mockStep2Context(dataAccess, dataAccessParams, dataAccessCell);
        Map<ApiType, Object> cachedApiValueDocument = new HashMap<>();
        for (DataAccessParams param : mockContext.getDataAccessParams()) {
            boolean success = paramValueIsCheck(mockContext, param, () -> paramValue(mockContext, param, apiParamMap, cachedApiValueDocument));
            if (!success) {
                log.warn("cqrcb data access next parse cancel by param check fail: cellId {}, param {}", cellId, param.getParamsName());
                // 参数校验失败，结束循环
                dataAccessCellRepository.delete(dataAccessCell);
                return;
            }
        }
        if (StringUtils.isNotEmpty(mockContext.getMessageId())) {
            dataAccessCell.setMessageId(mockContext.getMessageId());
        }
        dataAccessCell.setExtraData(JsonHelper.toJson(mockContext.getExtraData()));
        dataAccessCell.setParsedParams(JsonHelper.toJson(mockContext.getParsedParameters()));
        saveAndTriggerEvent(mockContext, dataAccessCell);
        log.info("cqrcb data access next parse end : cellId {}", cellId);
    }

    private Object paramValue(DataHubContextDto mockContext, DataAccessParams param, Map<String, ApiType> apiParamMap, Map<ApiType, Object> cachedApiValueDocument) {
        ApiType type = apiParamMap.get(param.getParamsName());
        if (type != null) {
            Object apiDocument = getApiValueDocument(type, mockContext, cachedApiValueDocument);
            if (apiDocument != null) {
                return paramValue(mockContext, param, apiDocument);
            }
        }
        return null;
    }

    private DataHubContextDto mockStep2Context(
            DataAccess dataAccess,
            List<DataAccessParams> dataAccessParams,
            DataAccessCell cell
    ) {
        RecordKey mockRecordKey = () -> {
        };
        DataHubDataDto mockDataHubDataDto = new DataHubDataDto(mockRecordKey, null);
        DataHubContextDto mockContext = new DataHubContextDto(mockDataHubDataDto, dataAccess, dataAccessParams);
        Map<String, Object> parsedParameters = JsonHelper.toMap(cell.getParsedParams());
        if (MapUtils.isNotEmpty(parsedParameters)) {
            mockContext.getParsedParameters().putAll(parsedParameters);
        }
        return mockContext;
    }

    private Map<String, ApiType> getApiParamMap(DataAccess dataAccess) {
        return getApiParamMap(dataAccess.getOrgId(), dataAccess.getId());
    }

    private Map<String, ApiType> getApiParamMap(Long orgId, Long dataAccessId) {
        ProjectCqrcbCompat compat = projectCqrcbCompatRepository.findFirstByOrgIdAndRelationIdAndType(orgId, dataAccessId, ProjectCqrcbCompatType.DATA_ACCESS_API_PARAM);
        Map<String, ApiType> apiParamMap = null;
        if (compat != null) {
            apiParamMap = JsonHelper.toMap2(compat.getConfig(), String.class, ApiType.class);
        }
        if (apiParamMap == null) {
            apiParamMap = new HashMap<>();
        }
        return apiParamMap;
    }

    /**
     * 可能返回null
     */
    private Object getApiValueDocument(ApiType type, DataHubContextDto context, Map<ApiType, Object> cachedApiValue) {
        Object document = cachedApiValue.get(type);
        if (!cachedApiValue.containsKey(type)) {
            IApiCall apiCall = apiCallMap.get(type);
            if (apiCall != null) {
                try {
                    String value = apiCall.call(context.getParsedParameters());
                    context.getExtraData().put(type.name(), value);
                    document = Configuration.defaultConfiguration().jsonProvider().parse(value);
                } catch (Throwable e) {
                    log.error("getApiValueMap {}", type, e);
                }
            }
            cachedApiValue.put(type, document);
        }
        return document;
    }
}
