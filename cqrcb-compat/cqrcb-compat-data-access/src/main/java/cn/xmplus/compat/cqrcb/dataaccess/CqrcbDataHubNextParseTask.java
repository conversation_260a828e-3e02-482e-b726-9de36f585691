package cn.xmplus.compat.cqrcb.dataaccess;


import lombok.extern.slf4j.Slf4j;
import org.befun.task.BaseTaskExecutor;
import org.befun.task.annotation.Task;
import org.befun.task.annotation.TaskRetryable;
import org.befun.task.dto.TaskContextDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Optional;

import static cn.xmplus.compat.cqrcb.CqrcbCompatCommonConfig.CQRCB_COMPAT_NEXT_PARSE_PREFIX;

@Slf4j
@Component
@Task("cqrcb-data-access-next-parse")
@TaskRetryable(maxAttempts = 3, disabled = false)
@ConditionalOnProperty(prefix = CQRCB_COMPAT_NEXT_PARSE_PREFIX, name = "enabled", havingValue = "true")
public class CqrcbDataHubNextParseTask extends BaseTaskExecutor<CqrcbDataHubNextParseTaskDetailDto> implements TransactionSynchronization {

    @Autowired
    private CqrcbDataHubParser cqrcbDataHubParser;
    private final ThreadLocal<CqrcbDataHubNextParseTaskDetailDto> localDetail = new ThreadLocal<>();

    @Override
    public void run(CqrcbDataHubNextParseTaskDetailDto detailDto, TaskContextDto taskContextDto) {
        cqrcbDataHubParser.parseStep2Params(detailDto.getOrgId(),
                detailDto.getDataAccessId(),
                detailDto.getCellId(),
                detailDto.getDataAccessParamIds()
        );
    }

    @Override
    public void performAsync(CqrcbDataHubNextParseTaskDetailDto data) {
        TransactionSynchronizationManager.registerSynchronization(this);
        localDetail.set(data);
    }

    @Override
    public void afterCompletion(int status) {
        if (status == STATUS_COMMITTED) {
            Optional.ofNullable(localDetail.get()).ifPresent(super::performAsync);
        }
        localDetail.remove();
        TransactionSynchronizationManager.clear();
    }
}
