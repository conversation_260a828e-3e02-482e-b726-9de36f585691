package cn.xmplus.compat.cqrcb.api;

import cn.xmplus.compat.cqrcb.property.CqrcbApiProperties;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpVersion;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.apache.http.protocol.HTTP;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.Map;

@Slf4j
public abstract class BaseApiCall implements IApiCall {

    @Autowired
    private CqrcbApiProperties apiProperties;

    @SneakyThrows
    @Override
    public String call(Map<String, Object> callParams) {
        String result = null;
        CqrcbApiProperties.CqrcbApi api = apiProperties.getApi().get(type());
        if (api != null) {
            int times = 0;
            int retryTimes = apiProperties.getRetryTimes();
            int retryInterval = apiProperties.getRetryInterval();
            while (times <= retryTimes) {
                try {
                    result = baseCall(api, callParams);
                } catch (Throwable e) {
                    log.error("{} error {}", getClass().getName(), e.getMessage());
                } finally {
                    times++;
                }

                if (StringUtils.isNotEmpty(result)) {
                    break;
                }
                if (times <= retryTimes && retryInterval > 0) {
                    sleep(retryInterval);
                }
            }
        }
        return result;
    }

    private String baseCall(CqrcbApiProperties.CqrcbApi api, Map<String, Object> params) throws IOException {
        String apiUrl = api.getUrl();
        if (StringUtils.isEmpty(apiUrl)) {
            throw new RuntimeException("url is empty");
        }
        apiUrl = TemplateEngine.renderTextTemplate(apiUrl, params);
        Map<String, Object> headers = TemplateEngine.renderJsonTemplate(api.getHeaders(), params);
        Request request = "post".equalsIgnoreCase(api.getMethod()) ? Request.Post(apiUrl) : Request.Get(apiUrl);
        if (headers != null) {
            headers.forEach((k, v) -> {
                if (v != null) {
                    request.addHeader(k, v.toString());
                }
            });
        }
        if ("post".equalsIgnoreCase(api.getMethod())) {
            Map<String, Object> body = TemplateEngine.renderJsonTemplate(api.getBody(), params);
            log.info("cqrcb {} api {} url: {} body: {}", type(), api.getMethod(), apiUrl, JsonHelper.toJson(body));
            request.bodyString(JsonHelper.toJson(body), ContentType.APPLICATION_JSON);
        }
        String response = request
                .connectTimeout(apiProperties.getHttpTimeOut())
                .socketTimeout(apiProperties.getHttpTimeOut())
                .version(HttpVersion.HTTP_1_0)
                .setHeader(HTTP.CONN_DIRECTIVE,HTTP.CONN_CLOSE)
                .execute()
                .returnContent()
                .asString();
        log.info("cqrcb {} api {} url: {} response: {}", type(), api.getMethod(), apiUrl, response);
        return parseResponse(api, response);
    }

    private String parseResponse(CqrcbApiProperties.CqrcbApi api, String response) throws IOException {
        // 没有设置怎么校验返回数据，直接成功
        if (StringUtils.isEmpty(api.getResponseCodeKey()) || StringUtils.isEmpty(api.getResponseSuccessCode())) {
            return response;
        }
        Map<String, Object> result = JsonHelper.toMap(response);
        if (result == null) {
            return null;
        }
        Object responseCode = result.get(api.getResponseCodeKey());
        if (responseCode == null || !api.getResponseSuccessCode().equals(responseCode.toString())) {
            // 校验返回数据失败
            return null;
        }
        // 如果没有设置怎么提取有效数据，则直接返回全部数据
        if (StringUtils.isEmpty(api.getResponseDataKey())) {
            return response;
        }
        // 提取有效数据，
        Object responseValue = result.get(api.getResponseDataKey());
        if (responseValue != null) {
            return JsonHelper.toJson(responseValue);
        }
        return null;
    }

    private void sleep(int ms) {
        try {
            Thread.sleep(ms);
        } catch (InterruptedException e) {
            // ignore
        }
    }
}
