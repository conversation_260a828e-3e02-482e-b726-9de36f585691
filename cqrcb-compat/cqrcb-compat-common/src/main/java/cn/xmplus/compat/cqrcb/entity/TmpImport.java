package cn.xmplus.compat.cqrcb.entity;

import lombok.Data;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/9/27/0027 11:21:16
 */
@Table(name = "tmp_import")
@Entity
@Data
public class TmpImport extends BaseEntity {

    @Column(name = "date")
    private String date;

    @Column(name = "data")
    private String data;

    @Column(name = "task_type")
    private int taskType;

    @Column(name = "file_name")
    private String fileName;
}











