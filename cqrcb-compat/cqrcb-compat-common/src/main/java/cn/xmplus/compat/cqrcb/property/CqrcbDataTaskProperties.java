package cn.xmplus.compat.cqrcb.property;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "project-compat.cqrcb.data-task")
public class CqrcbDataTaskProperties {
    /**
     * 数据库扫描cron
     */
    private String cron = "0 0/1 * * * ?";
}
