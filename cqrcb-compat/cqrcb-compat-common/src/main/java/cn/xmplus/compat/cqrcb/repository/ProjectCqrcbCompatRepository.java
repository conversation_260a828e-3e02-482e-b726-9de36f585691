package cn.xmplus.compat.cqrcb.repository;

import cn.xmplus.compat.cqrcb.consts.ProjectCqrcbCompatType;
import cn.xmplus.compat.cqrcb.entity.ProjectCqrcbCompat;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ProjectCqrcbCompatRepository extends ResourceRepository<ProjectCqrcbCompat, Long> {
    ProjectCqrcbCompat findFirstByOrgIdAndRelationIdAndType(Long orgId, Long relationId, ProjectCqrcbCompatType type);

}
