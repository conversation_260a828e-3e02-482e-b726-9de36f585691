package cn.xmplus.compat.cqrcb.api;

import cn.xmplus.compat.cqrcb.consts.ApiType;
import cn.xmplus.compat.cqrcb.property.CqrcbApiProperties;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class Mock1ApiCall implements IApiCall {

    @Autowired
    private CqrcbApiProperties apiProperties;

    public CqrcbApiProperties.CqrcbApiMock getApiMock() {
        return apiProperties.getApiMock().get(type());
    }

    @Override
    public ApiType type() {
        return ApiType.MOCK1;
    }

    @Override
    public String call(Map<String, Object> callParams) {
        boolean success = false;
        CqrcbApiProperties.CqrcbApiMock apiMock = apiProperties.getApiMock().get(type());
        if (apiMock != null) {
            int times = 0;
            int retryTimes = apiProperties.getRetryTimes();
            int retryInterval = apiProperties.getRetryInterval();
            while (times <= retryTimes) {
                if (times == 0) {
                    success = baseCall(apiMock.getSuccessRate(), apiMock.getMinCostTime(), apiMock.getMaxCostTime());
                } else if (times == 1) {
                    success = baseCall(apiMock.getRetrySuccessRate(), apiMock.getRetryMinCostTime(), apiMock.getRetryMaxCostTime());
                }
                if (!success) {
                    times++;
                    if (times <= retryTimes && retryInterval > 0) {
                        sleep(retryInterval);
                    }
                } else {
                    break;
                }
            }
        }
        Map<String, String> value = new HashMap<>();
        value.put(type().name().toLowerCase(), type().name());
        return JsonHelper.toJson(value);
    }

    private boolean baseCall(float successRate, int minCostTime, int maxCostTime) {
        double random = Math.random();
        boolean success = random > successRate;
        int costTime = Double.valueOf(random * (maxCostTime - minCostTime)).intValue() + minCostTime;
        sleep(costTime);
        return success;
    }

    private void sleep(int ms) {
        try {
            Thread.sleep(ms);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
