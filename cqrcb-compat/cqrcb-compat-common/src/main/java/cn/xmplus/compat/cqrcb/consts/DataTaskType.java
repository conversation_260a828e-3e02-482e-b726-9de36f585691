package cn.xmplus.compat.cqrcb.consts;

import cn.xmplus.compat.cqrcb.dto.OdsExportConfig;
import cn.xmplus.compat.cqrcb.dto.SyncOrganizeParam;
import cn.xmplus.compat.cqrcb.dto.SyncResponseParam;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum DataTaskType {

    // 导出任务
    EXPORT_DATA_TASK(OdsExportConfig.class, ""),
    IMPORT_DATA_TASK(SyncResponseParam.class,""),
    SYNC_ORGANIZE_TASK(SyncOrganizeParam.class,"");

    private final Class<?> paramClass;
    private final String triggerClassName;

    DataTaskType(Class<?> paramClass, String triggerClassName) {
        this.paramClass = paramClass;
        this.triggerClassName = triggerClassName;
    }
}
