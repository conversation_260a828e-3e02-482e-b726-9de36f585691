package cn.xmplus.compat.cqrcb.entity;

import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@EntityScopeStrategy
@Table(name = "project_cqrcb_credit_record")
public class ProjectCqrcbCreditRecord extends EnterpriseEntity {

    @Column(name = "push_id")
    private Long pushId;

    @Column(name = "serial_number")
    private String serialNumber;

    @Column(name = "euid")
    private String euid;

    @Column(name = "send_status")
    private String sendStatus;

    @Column(name = "send_times")
    private Integer sendTimes = 1;

    @Column(name = "request")
    private String request;

    @Column(name = "response")
    private String response;

    @Column(name = "is_complete")
    private Boolean isComplete;

}



