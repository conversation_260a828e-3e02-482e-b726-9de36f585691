package cn.xmplus.compat.cqrcb.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/10/13/0013 11:28:59
 */
@Getter
@Setter
public class SyncOrganizeParam {

    /**
     * 联系人电话
     */
    private String contactMobile;
    /**
     * 短信内容
     */
    private String contactContent;

    /**
     * 执行shell 脚本名称
     */
    private String shellName = "";
    /**
     * 请求系统
     */
    private String requestSystem = "CEM";

    /**
     * 来源系统
     */
    private String sourceSystem;
    /**
     * 机构表：DWS_ORG_INST_INFO_DD
     */
    private String sourceTable;
    /**
     * 分隔符
     */
    private String separator="@|@";
    /**
     * 报存目录
     */
    private String saveDir;
    /**
     * orgId
     */
    private Long orgId = -1L;
    /**
     * 读取文件是否跳过第一行
     */
    private boolean skipFirstLine;
    /**
     * * env环境
     */
    private String environment = "";

    /***************************/
    //false表示读取本地
    private boolean remote = false;
    private String host = "************";
    private int port = 22;
    private String username = "root";
    private String password = "";


}
