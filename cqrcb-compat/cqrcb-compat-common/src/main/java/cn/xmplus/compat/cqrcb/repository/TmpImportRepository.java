package cn.xmplus.compat.cqrcb.repository;

import cn.xmplus.compat.cqrcb.entity.TmpImport;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/27/0027 11:20:59
 */
public interface  TmpImportRepository extends ResourceRepository<TmpImport,Long> {

    List<TmpImport> findByDate(String date);

    List<TmpImport> findByDateAndFileNameLike(String date,String fileName);

    List<TmpImport> findByDateAndFileNameLikeAndIdGreaterThan(String date,String fileName,Long minId, Pageable pageable);

    boolean existsByDateAndFileName(String date,String fileName);

    List<TmpImport> findAllByDateLessThan(String date);

}
