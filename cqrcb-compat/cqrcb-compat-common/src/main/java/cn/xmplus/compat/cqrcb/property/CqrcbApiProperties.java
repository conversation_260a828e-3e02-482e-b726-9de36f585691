package cn.xmplus.compat.cqrcb.property;

import cn.xmplus.compat.cqrcb.consts.ApiType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "project-compat.cqrcb.api")
public class CqrcbApiProperties {

    /**
     * 重试次数
     */
    private int retryTimes = 1;
    /**
     * 重试间隔
     */
    private int retryInterval = 5000;

    private int httpTimeOut = 10000;

    private Map<ApiType, CqrcbApiMock> apiMock = new HashMap<>();
    private Map<ApiType, CqrcbApi> api = new HashMap<>();

    @Getter
    @Setter
    public static class CqrcbApiMock {
        private float successRate = 0.99f;
        private int minCostTime = 20;
        private int maxCostTime = 200;
        private float retrySuccessRate = 0.5f;
        private int retryMinCostTime = 20;
        private int retryMaxCostTime = 200;
    }

    @Getter
    @Setter
    public static class CqrcbApi {
        private String url; // https://dev.xmplus.cn/test?userId=${userId}&code=#{code}
        private String method = "get";
        private Map<String, Object> headers = new HashMap<>();
        private Map<String, Object> body = new HashMap<>();
        private String responseCodeKey;     // 响应的校验key             code
        private String responseSuccessCode; // 响应的校验key 对应的值     200
        private String responseDataKey;     // 提取有效的返回内容         data
    }
}
