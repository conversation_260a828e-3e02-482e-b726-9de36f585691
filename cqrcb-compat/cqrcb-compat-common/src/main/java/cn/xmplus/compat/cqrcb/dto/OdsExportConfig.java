package cn.xmplus.compat.cqrcb.dto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OdsExportConfig {

    /**
     * 紧急联系人手机号
     */
    private String contactMobile;
    private String contactContent = "【cem系统】--Ods数据导出任务--上传文件失败，请及时查看！";
    private String version = "v_1.0";
    /**
     * 文件留存天数
     */
    private int fileRetentionPeriod = 3;
    /**
     * 需要导出的问卷id
     */
    private List<Long> surveyIds = new ArrayList<>();
    /**
     * 导出目录
     */
    private String localPath = "/tmp/files/";
    /**
     * 分隔符
     */
    private String separator = "@|@";
    /**
     * 换行符
     **/
    private String lineBreak = "@|@@^@";
    /**
     * ods连接信息
     **/
    private boolean needConnect = true;
    private String host;
    private Integer port;
    private String username = "root";
    private String password = "";
    private String remotePath = "";
    private long retryInterval = 10000;
    /**
     * 规则文件数据
     */
    private List<String> ruleData = Arrays.asList("Q2@|@满意,非常满意,一般@|@null@|@null@|@Q2@|@0.2@|@1@|@4.5@|@10@|@0.7@|@30@|@100@|@0.7@|@200@|@@^@",
            "Q5@|@3.中介@|@null@|@null@|@1@|@1@|@0@|@4.5@|@10@|@0.7@|@30@|@100@|@0.7@|@200@|@@^@");

}
