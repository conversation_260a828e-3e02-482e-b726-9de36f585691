package cn.xmplus.compat.cqrcb.dto;

import cn.xmplus.compat.cqrcb.consts.ImportSourceTableType;
import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/28/0028 16:30:42
 */
@Data
public class SyncResponseParam {

    /**
     * 联系人电话
     */
    private String contactMobile;
    /**
     * 短信内容
     */
    private String contactContent;

    /**
     * 执行shell命令
     * "shell":["sh /a.sh cem dws，table ","sh /a.sh cem dwd，table "],
     */
    private List<String> shells = new ArrayList<>();

    /**
     * 报存文件目录
     */
    private String saveDir = "";

    /**
     * 神策分隔符
     */
    private String shenCeSeparator;

    /**
     * 人力分隔符
     */
    private String manPowerSeparator;

    /**
     * 问卷id列表
     */
    private List<Long> surveyIds;

    /**
     * 读取文件是否跳过第一行
     */
    private boolean skipFirstLine;

    //神策事件表
    private String shenceTable = "dwd_dprmp_sensors_senddata_di";
    //人力表
    private String renliTable = "dws_org_emp_info_dd";

    /**
     * 文件留存天数
     */
    private int fileRetentionPeriod = 3;
    private String filePattern = "\\d{8}";

}
