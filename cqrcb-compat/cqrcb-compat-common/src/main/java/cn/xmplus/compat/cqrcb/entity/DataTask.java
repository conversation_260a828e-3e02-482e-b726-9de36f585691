package cn.xmplus.compat.cqrcb.entity;

import cn.xmplus.compat.cqrcb.consts.DataTaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;

@Entity
@Getter
@Setter
@EntityScopeStrategy
@Table(name = "data_task")
public class DataTask extends BaseEntity {

    @Column(name = "task_type")
    @Schema(description = "定时任务类型")
    @Enumerated(EnumType.STRING)
    private DataTaskType taskType;

    @Column(name = "task_explain")
    @Schema(description = "定时任务描述")
    private String taskExplain;

    @Column(name = "status")
    @Schema(description = "任务状态，1:启用;2:停用")
    private Integer status;

    @Column(name = "cron")
    @Schema(description = "cron表达式")
    private String cron;

    @Column(name = "config")
    @Schema(description = "任务配置")
    private String config;

}



