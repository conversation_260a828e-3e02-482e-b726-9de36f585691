package cn.xmplus.compat.cqrcb.repository;

import cn.xmplus.compat.cqrcb.consts.DataTaskType;
import cn.xmplus.compat.cqrcb.entity.DataTask;
import java.util.List;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface DataTaskRepository extends ResourceRepository<DataTask, Long> {
    List<DataTask> findAllByStatusNot(int status);

    DataTask findFirstByTaskTypeAndStatus(DataTaskType taskType,int status);

}
