package cn.xmplus.compat.cqrcb.entity;

import cn.xmplus.compat.cqrcb.consts.DataTaskLogStatus;
import cn.xmplus.compat.cqrcb.consts.DataTaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name = "data_task_log")
public class DataTaskLog extends BaseEntity {

    @Column(name = "task_id")
    @Schema(description = "任务id")
    private Long taskId;

    @Column(name = "i_date")
    @Schema(description = "导入/导出日期")
    private String iDate;

    @Column(name = "type")
    @Schema(description = "导入/导出类型")
    @Enumerated(EnumType.STRING)
    private DataTaskType type;

    @Column(name = "status")
    @Schema(description = "导入/导出状态")
    private DataTaskLogStatus status;

    @Column(name = "fail_msg")
    @Schema(description = "失败原因")
    private String failMsg;

}



