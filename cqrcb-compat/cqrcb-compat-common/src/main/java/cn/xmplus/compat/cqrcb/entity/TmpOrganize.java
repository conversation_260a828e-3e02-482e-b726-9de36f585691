package cn.xmplus.compat.cqrcb.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tmp_organize")
public class TmpOrganize extends BaseEntity {

    @Column(name = "date")
    private String date;

    @Column(name = "data")
    private String data;

}






















