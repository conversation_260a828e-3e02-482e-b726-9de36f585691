package cn.xmplus.compat.cqrcb;


import cn.xmplus.compat.cqrcb.property.CqrcbApiProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import static cn.xmplus.compat.cqrcb.CqrcbCompatCommonConfig.CQRCB_COMPAT_PREFIX;
import static cn.xmplus.compat.cqrcb.CqrcbCompatCommonConfig.SCAN_PACKAGE;

@Configuration
@ComponentScan(SCAN_PACKAGE)
@EnableConfigurationProperties({CqrcbApiProperties.class})
@ConditionalOnProperty(prefix = CQRCB_COMPAT_PREFIX, name = "enabled", havingValue = "true")
public class CqrcbCompatCommonConfig {
    static final String SCAN_PACKAGE = "cn.xmplus.compat.cqrcb.api";
    public static final String CQRCB_COMPAT_PREFIX = "project-compat.cqrcb";
    public static final String CQRCB_COMPAT_NEXT_PARSE_PREFIX = "project-compat.cqrcb.next-parse";
    public static final String PACKAGE_ENTITY = "cn.xmplus.compat.cqrcb.entity";
    public static final String PACKAGE_REPOSITORY = "cn.xmplus.compat.cqrcb.repository";

}
