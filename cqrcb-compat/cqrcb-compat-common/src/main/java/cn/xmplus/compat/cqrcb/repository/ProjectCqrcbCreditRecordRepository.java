package cn.xmplus.compat.cqrcb.repository;

import cn.xmplus.compat.cqrcb.entity.ProjectCqrcbCreditRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Repository
public interface ProjectCqrcbCreditRecordRepository extends ResourceRepository<ProjectCqrcbCreditRecord, Long> {
    ProjectCqrcbCreditRecord findFirstByOrgIdAndPushId(Long orgId, Long pushId);

    List<ProjectCqrcbCreditRecord> findBySendStatusIsInAndIsCompleteAndSendTimesAndCreateTimeBetween(Collection<String> sendStatuses, Boolean isComplete, Integer sendTimes, Date createTimeStart, Date createTimeEnd, Pageable pageable);

    long countBySendStatusIsInAndIsCompleteAndSendTimesAndCreateTimeBetween(Collection<String> sendStatuses, Boolean isComplete, Integer sendTimes, Date createTimeStart, Date createTimeEnd);



}
