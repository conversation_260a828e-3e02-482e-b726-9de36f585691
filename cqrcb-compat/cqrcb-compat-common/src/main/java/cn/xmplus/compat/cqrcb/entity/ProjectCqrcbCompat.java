package cn.xmplus.compat.cqrcb.entity;

import cn.xmplus.compat.cqrcb.consts.ProjectCqrcbCompatType;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;

import javax.persistence.*;

@Entity
@Getter
@Setter
@EntityScopeStrategy
@Table(name = "project_cqrcb_compat")
public class ProjectCqrcbCompat extends EnterpriseEntity {

    @Column(name = "relation_id")
    private Long relationId;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private ProjectCqrcbCompatType type;

    @Column(name = "config")
    private String config;

}



