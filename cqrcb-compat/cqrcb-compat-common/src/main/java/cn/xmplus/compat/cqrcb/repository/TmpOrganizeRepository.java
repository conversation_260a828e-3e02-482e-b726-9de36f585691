package cn.xmplus.compat.cqrcb.repository;

import cn.xmplus.compat.cqrcb.entity.TmpOrganize;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface TmpOrganizeRepository extends ResourceRepository<TmpOrganize, Long> {

    List<TmpOrganize> findByDateAndIdGreaterThan(String date, Long minId, Pageable pageable);

    boolean existsByDate(String date);

    void deleteAllByDateLessThan(String date);

}
