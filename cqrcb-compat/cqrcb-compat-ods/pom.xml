<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>cqrcb-compat</artifactId>
    <groupId>cn.hanyi.compat</groupId>
    <version>${cqrcb-compat.version}</version>
    <relativePath>../pom.xml</relativePath>  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>cqrcb-compat-ods</artifactId>
  <name>cqrcb-compat-ods</name>
  <description>project-compat-ods</description>

  <dependencies>
    <dependency>
      <groupId>cn.hanyi.compat</groupId>
      <artifactId>cqrcb-compat-common</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.hanyi</groupId>
      <artifactId>cem-core</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.hanyi</groupId>
      <artifactId>survey-core</artifactId>
      <!--            <version>${survey.version}</version>-->
    </dependency>
    <dependency>
      <groupId>cn.hanyi</groupId>
      <artifactId>ctm-event-core</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.hanyi</groupId>
      <artifactId>survey-base</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-net</groupId>
      <artifactId>commons-net</artifactId>
      <version>3.8.0</version>
    </dependency>
  </dependencies>

  <properties>
    <maven.compiler.source>14</maven.compiler.source>
    <maven.compiler.target>14</maven.compiler.target>
  </properties>

</project>