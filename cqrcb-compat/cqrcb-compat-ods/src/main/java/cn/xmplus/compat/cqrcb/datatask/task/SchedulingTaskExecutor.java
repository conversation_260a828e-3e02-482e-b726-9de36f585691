package cn.xmplus.compat.cqrcb.datatask.task;

import cn.hutool.core.map.MapUtil;
import cn.xmplus.compat.cqrcb.consts.DataTaskLogStatus;
import cn.xmplus.compat.cqrcb.consts.DataTaskType;
import cn.xmplus.compat.cqrcb.datatask.service.SyncSurveyResponse;
import cn.xmplus.compat.cqrcb.entity.DataTask;
import cn.xmplus.compat.cqrcb.entity.DataTaskLog;
import cn.xmplus.compat.cqrcb.repository.DataTaskLogRepository;
import java.time.Duration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.AbortPolicy;
import java.util.concurrent.TimeUnit;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.SmsService;
import org.befun.task.repository.TaskProgressRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.config.CronTask;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Slf4j
public abstract class SchedulingTaskExecutor implements Runnable {

    protected DataTask dataTask;
    public CronTask cronTask;
    protected Object config;
    private static final int MAX_RETRY_COUNT = 3;
    protected DataTaskLog dataTaskLog;
    private final ForkJoinPool commonPool = new ForkJoinPool(
            Math.max(Runtime.getRuntime().availableProcessors(), 8));
    private final ExecutorService newPool = new ThreadPoolExecutor(0, commonPool.getParallelism(),
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(),  Executors.defaultThreadFactory(), new AbortPolicy());

    @Value("${project-compat.cqrcb.lock.prefix-name:cqrcb.lock}")
    private String prefixLockName;

    @Value("${project-compat.cqrcb.lock.ttl:120}")
    private Integer ttl;

    @Autowired
    private SmsService smsService;

    @Autowired
    private SyncSurveyResponse syncSurveyResponse;

    @Autowired
    private DataTaskLogRepository dataTaskLogRepository;

    @Autowired
    private TaskProgressRepository taskProgressRepository;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void run() {
        newPool.execute(() -> {
            initTaskLog();
            int i = 0;
            StringBuilder sb = new StringBuilder();
            boolean taskSuccessful = false;
            if (this.lock()) {
                log.info("已经有任务正在执行：{}", key());
                return;
            }
            while (i < MAX_RETRY_COUNT) {
                try {
                    this.task();
                    taskSuccessful = true;
                    break;
                } catch (Exception ex) {
                    log.error("任务：{}-{}执行异常，正在第{}次重试", dataTask.getId(), dataTask.getTaskExplain(),
                            i + 1);
                    sb.append(String.format("第%s次执行%s;", i + 1, ex.getMessage()));
                    ex.printStackTrace();
                }
                i++;
            }
            if (taskSuccessful) {
                log.info("任务：{}-{}执行成功", dataTask.getId(), dataTask.getTaskExplain());
                unlock();
            } else {
                log.info("任务：{}-{}执行失败", dataTask.getId(), dataTask.getTaskExplain());
                try {
                    Map<String, Object> config = JsonHelper.toMap(getConfig());
                    String phone = config.get("contactMobile") == null ? null : config.get("contactMobile").toString();
                    String content = config.get("contactContent") == null ? null : config.get("contactContent").toString();
                    this.sendMessage(phone, content, MapUtil.empty());
                } catch (Exception e) {
                    log.error("短信发送失败：{}", e.getMessage());
                } finally {
                    unlock();
                }
                this.writeLog(taskSuccessful, sb.toString());
            }
        });
    }

    private String key() {return prefixLockName + "." + this.getClass().getSimpleName().toLowerCase();}

    private Boolean lock() {return !stringRedisTemplate.opsForValue().setIfAbsent(key(), "1",
            Duration.ofSeconds(ttl));}

    private void unlock() {stringRedisTemplate.delete(key());}

    public abstract DataTaskType type();

    public abstract void task();

    public Object getConfig() {
        return config != null ? config : JsonHelper
                .toObject(this.dataTask.getConfig(), this.dataTask.getTaskType().getParamClass());
    }

    public abstract boolean isvalidConfig();

    public abstract boolean diffConfig(DataTask datatask);

    protected void writeLog(boolean status, String msg) {
        if (status) {
            dataTaskLog.setStatus(DataTaskLogStatus.SUCCESSED);
        } else {
            dataTaskLog.setStatus(DataTaskLogStatus.FAILED);
            dataTaskLog.setFailMsg(msg);
        }
        dataTaskLogRepository.save(dataTaskLog);
    }

    protected void sendMessage(String mobile, String content, Map<String, Object> param) {
        smsService.sendMessageByText(mobile, content, param);
    }

    public void initTaskLog() {
        DataTaskLog log = new DataTaskLog();
        log.setTaskId(dataTask.getId());
        log.setType(type());
        log.setIDate(syncSurveyResponse.getDateString(0));
        log.setType(type());
        log.setStatus(DataTaskLogStatus.INIT);
        dataTaskLogRepository.save(log);
        this.dataTaskLog = log;
    }

    public void updateTaskLogStatus(DataTaskLogStatus status, String msg) {
        if (dataTaskLog == null) {
            initTaskLog();
        }
        dataTaskLog.setStatus(status);
        dataTaskLog.setFailMsg(msg);
        dataTaskLogRepository.save(dataTaskLog);
    }
}
