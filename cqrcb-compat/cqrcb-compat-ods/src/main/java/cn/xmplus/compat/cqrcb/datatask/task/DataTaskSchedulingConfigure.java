package cn.xmplus.compat.cqrcb.datatask.task;

import cn.hutool.core.lang.Assert;
import cn.xmplus.compat.cqrcb.entity.DataTask;
import cn.xmplus.compat.cqrcb.property.CqrcbDataTaskProperties;
import cn.xmplus.compat.cqrcb.repository.DataTaskRepository;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.CronTask;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@EnableScheduling
@ConditionalOnProperty(prefix = "project-compat.cqrcb.data-task", name = "enabled", havingValue = "true")
public class DataTaskSchedulingConfigure implements SchedulingConfigurer {

    /**
     * 通过 registrar 可以注册 定时任务
     */
    private volatile ScheduledTaskRegistrar registrar;

    /**
     * 存放执行器
     */
    private final ConcurrentHashMap<Long, ScheduledFuture<?>> scheduledFutures = new ConcurrentHashMap<>();

    /**
     * 存放当前执行的定时任务
     */
    private final ConcurrentHashMap<Long, SchedulingTaskExecutor> runningTaskMap = new ConcurrentHashMap<>();

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    private DataTaskRepository dataTaskRepository;

    @Autowired
    private CqrcbDataTaskProperties dataTaskProperties;

    @Autowired(required = false)
    private List<SchedulingTaskExecutor> schedulingTaskRegistars;

    private Map<String, SchedulingTaskExecutor> registarMap = new HashMap<>();

    @PostConstruct
    private void setUp() {
        Optional.ofNullable(schedulingTaskRegistars).ifPresent(x->{
            registarMap = x.stream()
                    .collect(Collectors.toMap(c -> c.type().name(), Function
                            .identity()));
        });
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        this.registrar = scheduledTaskRegistrar;
        scheduledTaskRegistrar.addTriggerTask(
                () -> {
                    List<DataTask> dataTask = dataTaskRepository.findAll();
                    if (!dataTask.isEmpty()) {
                        createTask(dataTask);
                    }
                },
                triggerContext -> new CronTrigger(dataTaskProperties.getCron())
                        .nextExecutionTime(triggerContext)
        );
    }

    private void createTask(List<DataTask> dataTasks) {
        List<SchedulingTaskExecutor> schedulingTasks = new ArrayList<>();
        log.info("定时扫描dataTask任务");
        dataTasks.forEach(task -> {
            SchedulingTaskExecutor bean = registarMap.get(task.getTaskType().name());;
            if (bean != null) {
                bean.setDataTask(task);
                schedulingTasks.add(bean);
            }
        });
        refreshTask(schedulingTasks);
    }

    private void refreshTask(List<SchedulingTaskExecutor> tasks) {
        log.info("当前正在执行的dataTask任务===>{}",runningTaskMap.keySet());
        Set<Long> taskIds = scheduledFutures.keySet();
        for (SchedulingTaskExecutor task : tasks) {
            //校验配置是否有效
            if (task.isvalidConfig()) {
                if (scheduledFutures.containsKey(task.getDataTask().getId())) {
                    //如果任务配置发生变化，则取消当前任务
//                    if (task.diffConfig(
//                            runningTaskMap.get(task.getDataTask().getId()).getDataTask())) {
//                        scheduledFutures.remove(task.getDataTask().getId()).cancel(true);
//                        runningTaskMap.remove(task.getDataTask().getId());
//                    } else {
//                        continue;
//                    }
                    if (task.getDataTask().getStatus() != 1) {
                        scheduledFutures.remove(task.getDataTask().getId()).cancel(true);
                        runningTaskMap.remove(task.getDataTask().getId());
                    } else {
                        continue;
                    }
                }
                if (task.getDataTask().getStatus() == 1) {
                    CronTask cronTask = new CronTask(task, task.getDataTask().getCron());
                    task.cronTask = cronTask;
                    if (scheduledFutures.containsKey(task.getDataTask().getId())) {
                        scheduledFutures.remove(task.getDataTask().getId()).cancel(true);
                    }
                    ScheduledFuture<?> future = Objects.requireNonNull(registrar.getScheduler())
                            .schedule(cronTask.getRunnable(), cronTask.getTrigger());
                    runningTaskMap.put(task.getDataTask().getId(), task);
                    Assert.notNull(future, "dataTask定时任务出错");
                    scheduledFutures.put(task.getDataTask().getId(), future);
                    log.info("添加定时任务:{}===>{}", task.getDataTask().getId(),
                            task.getDataTask().getConfig());
                }
            }
        }
    }

    private boolean exists(Long taskId, List<SchedulingTaskExecutor> tasks) {
        for (SchedulingTaskExecutor task : tasks) {
            if (task.getDataTask().getId().equals(taskId)) {
                return true;
            }
        }
        return false;
    }

    @Bean
    public ThreadPoolTaskScheduler taskScheduler(){
        ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
        //threadPoolTaskScheduler.setPoolSize(2);
        return threadPoolTaskScheduler;
    }
}
