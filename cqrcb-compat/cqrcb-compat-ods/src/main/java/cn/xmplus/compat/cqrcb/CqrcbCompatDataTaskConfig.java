package cn.xmplus.compat.cqrcb;


import static cn.xmplus.compat.cqrcb.CqrcbCompatCommonConfig.CQRCB_COMPAT_PREFIX;
import static cn.xmplus.compat.cqrcb.CqrcbCompatDataTaskConfig.SCAN_PACKAGE;

import cn.xmplus.compat.cqrcb.property.CqrcbDataTaskProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;


@Configuration
@ComponentScan(SCAN_PACKAGE)
@EnableConfigurationProperties(CqrcbDataTaskProperties.class)
@ConditionalOnProperty(prefix = CQRCB_COMPAT_PREFIX, name = "enabled", havingValue = "true")
public class CqrcbCompatDataTaskConfig {
    static final String SCAN_PACKAGE = "cn.xmplus.compat.cqrcb.datatask";
}
