package cn.xmplus.compat.cqrcb.datatask.utils;

import cn.hanyi.survey.core.dto.task.ResponseDetailDto;
import cn.xmplus.compat.cqrcb.consts.DataTaskLogStatus;
import cn.xmplus.compat.cqrcb.datatask.dto.OdsExportData;
import cn.xmplus.compat.cqrcb.datatask.dto.OdsExportContext;
import cn.xmplus.compat.cqrcb.dto.OdsExportConfig;
import java.io.BufferedInputStream;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream;

@Slf4j
public class OdsExport {

    private OdsExportContext context;
    private OdsExportConfig config;
    private static final int MAX_RETRY_COUNT = 3;
    private static final String DATE_PATTERN = "yyyyMMdd";

    public OdsExport(OdsExportContext context, OdsExportConfig config) {
        this.context = context;
        this.config = config;
    }

    /**
     * 创建空文件，写入表头
     */
    public void initDataFile() {
        try {
            StringBuilder headers = new StringBuilder();
            Arrays.asList(OdsExportData.class.getDeclaredFields()).forEach(f -> {
                headers.append(f.getName());
                headers.append(config.getSeparator());
            });
            headers.append(config.getLineBreak());
            context.getWriter().write(headers.toString());
            context.getWriter().newLine();
            context.getWriter().flush();
            context.setStatus(DataTaskLogStatus.RUNNING);
        } catch (IOException ex) {
            log.error("初始化写入数据文件失败");
            ex.printStackTrace();
        }
    }

    public void finishExport() {
        try {
            if (context.getBosWriter() != null) {
                context.getBosWriter().close();
            }
            if (context.getWriter() != null) {
                context.getWriter().close();
            }
            if (context.getDataFile() != null && context.getDataFile().exists()) {
                context.getDataFile().deleteOnExit();
            }
            // 文件保留三天清除
            try (Stream<Path> walk = Files.walk(Paths.get(config.getLocalPath()))) {
                walk.filter(path -> path.toFile().isFile() && (
                        path.getFileName().toString().matches(context.getClearFileNamePattern())))
                    .filter(path -> {
                        try {
                            BasicFileAttributes attrs = Files
                                    .readAttributes(path, BasicFileAttributes.class);
                            LocalDate lastModified = attrs.lastModifiedTime().toInstant()
                                    .atZone(
                                            ZoneId.systemDefault()).toLocalDate();
                            return lastModified.isBefore(LocalDate.now().minusDays(
                                    config.getFileRetentionPeriod()));
                        } catch (Exception e) {
                            e.printStackTrace();
                            return false;
                        }})
                    .forEach(path -> {
                        if (path.toFile().delete()) {
                            log.info("清除文件,{}", path.toFile().getName());
                        }
                    });
            }
        } catch (Exception ex) {
            log.warn("close writer failed");
        }
    }

    /**
     * 生成数据文件
     *
     * @param data
     */
    public void writeDataFile(List<OdsExportData> data) throws IOException {
        if (context.getStatus() == DataTaskLogStatus.INIT) {
            context.setDataFile(new File(context.getTxtFileNameWithPath()));
            context.setWriter(new BufferedWriter(new FileWriter(context.getDataFile())));
            context.setStatus(DataTaskLogStatus.RUNNING);
//            initDataFile();
        }
        StringBuilder stringBuilder = new StringBuilder();
        data.forEach(d -> {
            String[] value = d.toStringArray();
            String row = String.join(config.getSeparator(), value) + config.getSeparator() + config
                    .getLineBreak() + System.lineSeparator();
            stringBuilder.append(row);
        });
        try {
            log.info("数据写入成功:{}", data.size());
            context.getWriter().write(stringBuilder.toString());
            context.getWriter().flush();
            context.setSuccessed(context.getSuccessed() + data.size());
        } catch (Exception exception) {
            List<Long> failedId = data.stream().map(OdsExportData::getId).collect(
                    Collectors.toList());
            log.error("数据写入失败：{}", failedId);
            context.setFailed(context.getFailed() + 1);
            context.getResult().getFailedResponseId().addAll(failedId);
            context.getResult().getReasons().add("数据写入失败" + failedId);
            exception.printStackTrace();
        }
    }

    /**
     * 生成校验文件
     *
     * @return
     */
    public void writeCheckFile() throws IOException {
        BufferedWriter bufferedWriter = new BufferedWriter(
                new OutputStreamWriter(context.getCheckBos()));
        String data = String
                .format("%s@|@CEM@|@surveyResponse@|@%s@|@0@|@@^@", LocalDate.now().format(
                        DateTimeFormatter.ofPattern(DATE_PATTERN)), context.getTotal());
        bufferedWriter.write(data);
        bufferedWriter.close();
        log.info("生成校验文件成功");
    }

    /**
     * 生成规则文件
     */
    public void writeRuleDataFile() throws IOException {
        BufferedWriter bufferedWriter = new BufferedWriter(
                new OutputStreamWriter(context.getRuleBos()));
        for (String data : config.getRuleData()) {
            bufferedWriter.write(data);
            bufferedWriter.flush();
            bufferedWriter.newLine();
        }
        bufferedWriter.close();
        log.info("生成规则文件成功");
    }

    /**
     * 压缩打包文件
     *
     * @return
     * @throws IOException
     */
    public File packageFile() throws IOException {
        File packageFile = new File(context.getPackageFileNameWithPath());
        try (
                FileOutputStream fos = new FileOutputStream(packageFile);
                GzipCompressorOutputStream gzos = new GzipCompressorOutputStream(fos);
                TarArchiveOutputStream tos = new TarArchiveOutputStream(gzos)
        ) {
            byte[] bytes = new byte[2048];
            // 数据文件压缩
            try (
                    InputStream is = new FileInputStream(context.getDataFile());
                    BufferedInputStream bis = new BufferedInputStream(is)
            ) {
                TarArchiveEntry dataEntry = new TarArchiveEntry(context.getTxtFileName());
                dataEntry.setSize(context.getDataFile().length());
                tos.putArchiveEntry(dataEntry);
                int bytesRead;
                while ((bytesRead = bis.read(bytes)) != -1) {
                    tos.write(bytes, 0, bytesRead);
                }
                tos.closeArchiveEntry();
            }
            // 校验文件压缩
            writeCheckFile();
            TarArchiveEntry checkEntry = new TarArchiveEntry(context.getCheckFileName());
            checkEntry.setSize(context.getCheckBos().size());
            tos.putArchiveEntry(checkEntry);
            tos.write(context.getCheckBos().toByteArray());
            tos.closeArchiveEntry();

            // 规则文件压缩
            writeRuleDataFile();
            TarArchiveEntry ruleEntry = new TarArchiveEntry(context.getRuleFileName());
            ruleEntry.setSize(context.getRuleBos().size());
            tos.putArchiveEntry(ruleEntry);
            tos.write(context.getRuleBos().toByteArray());
            tos.closeArchiveEntry();

            tos.finish();
            tos.close();
            gzos.close();
            fos.close();
            return packageFile;
        }
    }

    /**
     * 上传文件
     *
     * @param file
     * @return
     * @throws InterruptedException
     */
    public boolean upload(File file) throws InterruptedException {
        int retryCount = 0;
        boolean dataSuccess = false;
        boolean endSuccess = false;
        while ((!dataSuccess || !endSuccess) && retryCount < MAX_RETRY_COUNT) {
            OdsClient odsClient = new OdsClient(config.getHost(), config.getPort(),
                    config.getUsername(), config.getPassword());
            try {
                if (odsClient.login()) {
                    // 上传压缩文件
                    if (!dataSuccess) {
                        dataSuccess = odsClient.upload(file, config.getRemotePath());
                    }
                    // 上传结束文件
                    if (!endSuccess) {
                        File endFile = new File(context.getEndFileNameWithPath());
                        endFile.createNewFile();
                        endSuccess = odsClient.upload(endFile, config.getRemotePath());
                    }
                }
                retryCount ++;
            } catch (IOException ex) {
                log.error("上传文件失败");
                retryCount++;
                context.setStatus(DataTaskLogStatus.FAILED);
                context.getResult().getReasons().add("上传文件失败" + ex.getMessage());
            } finally {
                odsClient.logout();
            }
            if (!(dataSuccess && endSuccess)) {
                Thread.sleep(config.getRetryInterval());
            }
        }
        return dataSuccess && endSuccess;
    }
}
