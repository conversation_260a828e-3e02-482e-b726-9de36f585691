package cn.xmplus.compat.cqrcb.datatask.service;

import cn.hanyi.survey.core.dto.message.SurveyResponseCellMessageDto;
import cn.hanyi.survey.core.dto.message.SurveyResponseMessageDto;
import cn.hanyi.survey.core.dto.task.ResponseDetailDataDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import cn.hanyi.survey.service.ResponseService;
import cn.xmplus.compat.cqrcb.datatask.dto.OdsExportData;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ResponseProcessService {

    @Autowired
    private ResponseService responseService;

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    /**
     * 答卷数据需要事务处理懒加载数据
     *
     * @param survey
     * @param responseIds
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<OdsExportData> processResponse(Survey survey, List<SurveyChannel> channelList,
            List<Long> responseIds) {
        log.info("组装答卷数据：{}", responseIds);
        List<SurveyResponse> surveyResponses = responseService.getByIds(responseIds);
        return surveyResponses.stream().map(r -> {
            SurveyResponseMessageDto messageDto = new SurveyResponseMessageDto(
                    survey, r, r.getCells());
            messageDto.getData().forEach(SurveyResponseCellMessageDto::convertText);
            List<ResponseDetailDataDto> detailDataDtos = messageDto.getData()
                    .stream().map(d -> {
                        ResponseDetailDataDto detailDataDto = JsonHelper.getObjectMapper()
                                .convertValue(d, ResponseDetailDataDto.class);
                        detailDataDto.setType(String.valueOf(d.getType().getText()));
                        return detailDataDto;
                    }).collect(Collectors.toList());
            return new OdsExportData(survey, r, channelList, detailDataDtos);
        }).collect(Collectors.toList());
    }

    /**
     * 问卷数据需要事务处理懒加载数据
     *
     * @param surveyId
     * @return
     */
    public Survey getSurvey(Long surveyId) {
        Survey survey = surveyBaseEntityService.require(Survey.class, surveyId);
        Hibernate.initialize(survey.getQuestions());
        survey.getQuestions().forEach(q -> {
            Hibernate.initialize(q.getItems());
            Hibernate.initialize(q.getColumns());
        });
        return survey;
    }
}
