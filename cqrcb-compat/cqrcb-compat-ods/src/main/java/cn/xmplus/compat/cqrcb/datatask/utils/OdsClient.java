package cn.xmplus.compat.cqrcb.datatask.utils;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.TimeZone;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.net.ftp.FTPSClient;

@Slf4j
public class OdsClient {

    private FTPSClient ftpsClient = new FTPSClient();
    private String host;
    private Integer port;
    private String username;
    private String password;

    public OdsClient(String host, Integer port, String username, String password) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
    }

    /**
     * 连接ods
     *
     * @return
     */
    public boolean login() {
        boolean isLogin = false;
        FTPClientConfig clientConfig = new FTPClientConfig();
        clientConfig.setServerTimeZoneId(TimeZone.getDefault().getID());
        this.ftpsClient.configure(clientConfig);
        try {
            this.ftpsClient.connect(this.host, this.port);
            int replyCode = this.ftpsClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                this.ftpsClient.disconnect();
                log.info("ods 服务器连接失败");
                return false;
            }
            this.ftpsClient.login(this.username, this.password);
            this.ftpsClient.execPBSZ(0);
            this.ftpsClient.execPROT("P");
            this.ftpsClient.enterLocalPassiveMode();
            this.ftpsClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            log.info("ods 服务器登陆成功");
            isLogin = true;
        } catch (Exception ex) {
            log.error("ods 服务器登陆失败, caused by:{}", ex.getMessage());
        }
        this.ftpsClient.setBufferSize(2048);
        return isLogin;
    }

    /**
     * 退出ods服务器
     */
    public void logout() {
        if (this.ftpsClient != null && this.ftpsClient.isConnected()) {
            try {
                boolean res = this.ftpsClient.logout();
                if (res) {
                    log.info("ods 服务器退出成功");
                }
            } catch (IOException ex) {
                log.warn("ods 服务器退出异常，caused by {}", ex.getMessage());
            } finally {
                try {
                    this.ftpsClient.disconnect();
                } catch (IOException ex) {
                    log.warn("ods 服务器关闭连接异常，caused by {}", ex.getMessage());
                }
            }
        }
    }

    /**
     * 上传文件
     *
     * @param file
     * @param remoteUploadPath
     * @return
     * @throws IOException
     */
    public boolean upload(File file, String remoteUploadPath) throws IOException {
        BufferedInputStream inputStream = null;
        boolean success = false;
        try {
            this.ftpsClient.changeWorkingDirectory(remoteUploadPath);
            inputStream = new BufferedInputStream(new FileInputStream(file));
            log.info("ods 开始上传文件, {}", file.getName());
            success = this.ftpsClient.storeFile(file.getName(), inputStream);
            if (success) {
                log.info("ods 上传文件成功, {}", file.getName());
            } else {
                log.info("ods 上传文件失败, {}", file.getName());
            }
        } catch (FileNotFoundException ex) {
            log.error("ods 上传文件不存在，caused by {}", ex.getMessage());
        } catch (Exception ex) {
            log.error("ods 上传文件失败，caused by {}", ex.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException ex) {
                    log.warn("close inputStream failed");
                    ex.printStackTrace();
                }
            }
        }
        return success;
    }

    public static void main(String[] args) {
        long localDate = LocalDate.now().minusDays(3).toEpochDay();
        Date date = new Date(localDate);
        System.out.println("sss");
    }
}
