package cn.xmplus.compat.cqrcb.datatask.dto;

import cn.xmplus.compat.cqrcb.consts.DataTaskLogStatus;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.utils.JsonHelper;

@Getter
@Setter
public class OdsExportContext {

    private long total;
    private int successed;
    private int failed;
    private List<Long> responseId;
    private ExportResult result = new ExportResult();

    private BufferedWriter bosWriter;
    private BufferedWriter writer;
    private ByteArrayOutputStream dataBos = new ByteArrayOutputStream();
    private ByteArrayOutputStream checkBos = new ByteArrayOutputStream();
    private ByteArrayOutputStream ruleBos = new ByteArrayOutputStream();
    private File dataFile;

    // 上传文件使用

    private String basePath = "";
    private String txtFileName = "cem_survey_response_info_${date}.txt";
    private String checkFileName = "cem_check_${date}";
    private String ruleFileName = "cem_ruleData_${date}";
    private String packageFileName = "cem_${date}.tar.gz";
    private String endFileName = "cem_${date}_end";
    private String clearFileNamePattern = "cem_\\d{8}_end|cem_\\d{8}.tar.gz|cem_survey_response_info_\\d{8}.txt";

    private DataTaskLogStatus status = DataTaskLogStatus.INIT;

    public void fillFileName(String date) {
        this.txtFileName = txtFileName.replace("${date}", date);
        this.checkFileName = checkFileName.replace("${date}", date);
        this.packageFileName = packageFileName.replace("${date}", date);
        this.endFileName = endFileName.replace("${date}", date);
        this.ruleFileName = ruleFileName.replace("${date}", date);
    }

    public String getTxtFileNameWithPath() {
        return basePath + txtFileName;
    }

    public String getCheckFileNameWithPath() {
        return basePath + checkFileName;
    }

    public String getPackageFileNameWithPath() {
        return basePath + packageFileName;
    }

    public String getEndFileNameWithPath() {
        return basePath + endFileName;
    }

    public String jsonResult() {
        return String
                .format("{\"total\":%s, \"successed\":%s, \"failed\":%s, \"result\":%s",
                        total,
                        successed,
                        failed,
                        JsonHelper.toJson(this.getResult()));
    }

    @Getter
    @Setter
    public static class ExportResult {

        private List<Long> failedResponseId = new ArrayList<>();
        private List<String> reasons = new ArrayList<>();
    }
}
