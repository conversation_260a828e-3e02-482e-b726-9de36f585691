package cn.xmplus.compat.cqrcb.datatask.service;


import cn.hutool.core.lang.Assert;
import cn.xmplus.compat.cqrcb.consts.DataTaskType;
import cn.xmplus.compat.cqrcb.datatask.task.SchedulingTaskExecutor;
import cn.xmplus.compat.cqrcb.dto.SyncOrganizeParam;
import cn.xmplus.compat.cqrcb.dto.SyncResponseParam;
import cn.xmplus.compat.cqrcb.entity.DataTask;
import cn.xmplus.compat.cqrcb.entity.TmpOrganize;
import cn.xmplus.compat.cqrcb.repository.DataTaskRepository;
import cn.xmplus.compat.cqrcb.repository.TmpOrganizeRepository;
import com.alibaba.fastjson.JSON;
import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.entity.Department;
import org.befun.auth.repository.DepartmentRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.task.annotation.TaskLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.CompactNumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR>
 * @date 2023/6/9 13:43:06
 */

@Slf4j
@Component
public class SyncOrganizeService {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyMMdd");

    private static final int batchSize = 500;

    private static Session session = null;

    private static ChannelSftp channelSftp = null;

    private static ChannelExec channelExec = null;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private TmpOrganizeRepository tmpOrganizeRepository;

    @Autowired
    private DataTaskRepository dataTaskRepository;

    @Autowired
    private SyncSurveyResponse syncSurveyResponse;


    public SyncOrganizeParam getParam() {
        DataTask dataTask = dataTaskRepository.findFirstByTaskTypeAndStatus(DataTaskType.SYNC_ORGANIZE_TASK, 1);
        if (dataTask == null) {
            throw new RuntimeException(DataTaskType.IMPORT_DATA_TASK + "任务未启用或不存在");
        }
        return JsonHelper.toObject(dataTask.getConfig(), SyncOrganizeParam.class);
    }

    public void syncOrganizeRemote(SyncOrganizeParam config) {
        try {
            this.connectServer(config.getHost(), config.getPort(), config.getUsername(), config.getPassword());
            this.exeShell(config);
            String rootDir = this.getRootDir(config.getSaveDir());
            List<String> fileList = this.getRemoteFileList(rootDir);
            this.syncFileToTmpOrganize(true, fileList);
            this.pageParseTmpOrganizeToDepartment(config);
        } finally {
            this.pageParseTmpOrganizeToDepartment(config);
            updateTree(config.getOrgId());
            log.info("update tree success");
            this.closeConnect();
        }
    }

    public void syncOrganizeLocal(SyncOrganizeParam config) {
        try {
            this.exeShell(config);
            String rootDir = this.getRootDir(config.getSaveDir());
            List<String> fileList = this.getLocalFileList(rootDir);
            this.syncFileToTmpOrganize(false, fileList);
            this.pageParseTmpOrganizeToDepartment(config);
        } finally {
            this.pageParseTmpOrganizeToDepartment(config);
            updateTree(config.getOrgId());
            log.info("update tree success");
        }
    }

    public void exeShell(SyncOrganizeParam param) {
        String table = param.getSourceTable();
            Optional.ofNullable(table).ifPresent(x -> {
                String shell = syncSurveyResponse.getCompleteShell(param.getShellName(), param.getRequestSystem(),
                        param.getSourceSystem(), table, param.getSaveDir(), param.getEnvironment());
                log.info("shell脚本：{}", shell);
                try {
                    Process process = Runtime.getRuntime().exec(shell);
                    int code = process.waitFor();
                    if (code == 0) {
                        log.info("执行{}成功返回码：{}", shell, code);
                    } else {
                        log.warn("执行{}失败返回码：{}", shell, code);
                        throw new BadRequestException(String.format("执行{}失败返回码：{}", shell, code));
                    }
                } catch (IOException | InterruptedException e) {
                    throw new RuntimeException(String.format("shell脚本(%s)执行出错\n%s", shell, e.getMessage()));
                }
            });
    }


    public void connectServer(String host, int port, String username, String password) {
        try {
            JSch jsch = new JSch();
            session = jsch.getSession(username, host, port);
            session.setPassword(password);
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect();
            log.info("session连接成功");

            channelSftp = (ChannelSftp) session.openChannel("sftp");
            channelSftp.connect();
            log.info("channelSftp连接成功");

        } catch (JSchException e) {
            log.error("连接ssh失败，username:{},host:{},port:{},password:{}", username, host, port, password);
            throw new BadRequestException(e.getMessage());
        }
    }

    public void syncFileToTmpOrganize(boolean isRemote, List<String> fileList) {
         boolean exist = tmpOrganizeRepository.existsByDate(getDateString(0));
         if(exist){
             return;
         }
         fileList.forEach(filename -> {
            log.info("====开始同步文件到临时表：{}====", filename);
            try (
                    InputStream is = isRemote ? channelSftp.get(filename) : new FileInputStream(filename);
                    GZIPInputStream gzipInputStream = new GZIPInputStream(is);
                    InputStreamReader inputStreamReader = new InputStreamReader(gzipInputStream, StandardCharsets.UTF_8);
                    BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {

                String line;
                int count = 0;
                boolean skipFirstLine = this.getParam().isSkipFirstLine();
                while ((line = bufferedReader.readLine()) != null) {
                    if(skipFirstLine){
                        skipFirstLine = false;
                        continue;
                    }
                    saveOrganizeToTmpDB(line);
                    count++;
                }
                log.info("同步文件到临时表{}结束，共{}行", filename, count);
            } catch (IOException | SftpException e) {
                log.error("读取文件{}失败:{}", filename, e.getMessage());
            }
        });
    }

    public void pageParseTmpOrganizeToDepartment(SyncOrganizeParam config) {
        long minId = 0;
        boolean hasData = true;
        while (hasData) {
            //每次查询500条解析
            List<TmpOrganize> tmpOrganizes = tmpOrganizeRepository.findByDateAndIdGreaterThan(this.getDateString(0), minId, PageRequest.of(0, batchSize, Sort.by("id")));
            if (CollectionUtils.isNotEmpty(tmpOrganizes)) {
                syncTmpOrganizeToDepartment(tmpOrganizes, config);
                minId = tmpOrganizes.get(tmpOrganizes.size() - 1).getId();
            } else {
                hasData = false;
            }
        }
    }

    public void syncTmpOrganizeToDepartment(List<TmpOrganize> tmpOrganizes, SyncOrganizeParam config) {
        log.info("====开始同步临时表到组织架构====");
        try {
            Department root = departmentRepository.findFirstByOrgIdAndPid(config.getOrgId(), 0L);
            if (root == null) {
                log.error("父级不存在");
                return;
            }
            tmpOrganizes.stream().forEach(tmpOrganize -> {
                saveDepartment(tmpOrganize.getData(), root, config);
            });
            log.info("====同步临时表到组织架构结束====");
        } catch (Exception e) {
            log.error("同步临时表到组织架构异常", e.getMessage());
        }
    }

    /**
     * 读取文件的数据保存到临时表
     */
    public void saveOrganizeToTmpDB(String line) {
        TmpOrganize tmpOrganize = new TmpOrganize();
        tmpOrganize.setDate(getDateString(0));
        tmpOrganize.setData(line);
        tmpOrganizeRepository.save(tmpOrganize);
    }

    public String getDateString(int day) {
        return LocalDate.now().plusDays(day).format(formatter);
    }

    public void saveDepartment(String line, Department root, SyncOrganizeParam config) {
        if (StringUtils.isEmpty(line)) {
            return;
        }
        try {
            //data[1]机构编号，data[2]机构名称，data[15]父级机构号 机构名称为纯数字的过滤
            String[] data = line.split(config.getSeparator());
            //data[]:营业标识
            if (data.length >= 15 && StringUtils.isNotEmpty(data[3]) && !StringUtils.isNumeric(data[3])) {
                Department existDepartment = departmentRepository.findFirstByOrgIdAndCode(config.getOrgId(), data[1]);
                if (existDepartment == null) {
                    Department department = new Department();
                    department.setCode(data[1]);
                    department.setTitle(data[3]);
                    department.setOrgId(config.getOrgId());
                    department.setPid(root.getId());
                    Department parent = departmentRepository.findFirstByOrgIdAndCode(config.getOrgId(), data[15]);
                    if (parent != null) {
                        department.setPid(parent.getId());
                    }
                    departmentRepository.save(department);
                } else {
                    //存在部门且标题不一样更新
                    String subTitle = data[3];
                    if (subTitle != null && !subTitle.equals(existDepartment.getTitle())) {
                        existDepartment.setTitle(subTitle);
                    }
                    Department parent = departmentRepository.findFirstByOrgIdAndCode(config.getOrgId(), data[15]);
                    if (parent != null && !"000000".equals(data[1])) {
                        existDepartment.setPid(parent.getId());
                    }
                    departmentRepository.save(existDepartment);
                }
            }
        } catch (Exception e) {
            log.error("save department fail ：line={},message={}", line, e.getMessage());
        }
    }

    public String subTitle(String title) {
        if (title != null) {
            String start = "重庆农村商业银行股份有限公司";
            String start2 = "重庆农村商业银行";
            if (title.startsWith(start)) {
                return title.substring(start.length(), title.length());
            } else if (title.startsWith(start2)) {
                return title.substring(start2.length(), title.length());
            } else {
                return title;
            }
        }
        return null;
    }

    public void updateTree(Long orgId) {
        Assert.notNull(orgId, "clear tree fail,orgId is null");
        departmentService.clearCache(orgId);
        departmentService.updateParentList(orgId);
    }

    public String getRootDir(String rootDir) {
        if (!rootDir.endsWith("/")) {
            rootDir += "/";
        }
        String dateString = LocalDate.now().minusDays(1).format(formatter);
        return rootDir + dateString;
    }

    /**
     * 获取远程服务器path所有文件路径
     *
     * @param dirPath
     * @return
     */
    public List<String> getRemoteFileList(String dirPath) {
        Assert.notEmpty(dirPath);
        List<String> result = new ArrayList<>();
        if (!dirPath.endsWith("/")) {
            dirPath += "/";
        }
        Vector<ChannelSftp.LsEntry> files = null;
        try {
            files = channelSftp.ls(dirPath);
        } catch (SftpException e) {
            log.error("path路径不正确：path={}", dirPath);
            throw new BadRequestException("执行 channelSftp.ls(path) 异常");
        }
        if (files == null || files.size() == 0) {
            return result;
        }
        for (int i = 0; i < files.size(); i++) {
            ChannelSftp.LsEntry temp = files.get(i);
            if (temp.getFilename().startsWith(".")) {
                continue;
            }
            if (temp.getAttrs().isDir()) {
                List<String> dirList = getRemoteFileList(dirPath + temp.getFilename());
                result.addAll(dirList);
            } else {
                if (temp.getFilename().contains("org_inst_info") && !temp.getFilename().startsWith("check")) {
                    result.add(dirPath + temp.getFilename());
                }
            }
        }
        log.info("共{}个文件，文件列表：{}", result.size(), result.toString());
        return result;
    }

    public List<String> getLocalFileList(File file) {
        Objects.nonNull(file);
        List<String> result = new ArrayList<>();
        if (!file.exists()) {
            log.warn("{} 不存在", file.getPath());
            return result;
        }
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File temp : files) {
                List<String> dirList = getLocalFileList(temp);
                result.addAll(dirList);
            }
        } else {
            //只取机构文件
            if (file.getName().contains("org_inst_info") && !file.getName().startsWith("check")) {
                result.add(file.getPath());
            }
        }
        log.info("共{}个文件，文件列表：{}", result.size(), result.toString());
        return result;
    }

    public List<String> getLocalFileList(String path) {
        File file = new File(path);
        return getLocalFileList(file);
    }

    public boolean isGzFile(InputStream is) {
        byte[] bytes = new byte[2];
        try {
            is.read(bytes);
            return bytes[0] == 0x1f && (bytes[1] & 0xFF) == 0x8b;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 关闭连接
     */
    public void closeConnect() {

        if (channelSftp != null) {
            channelSftp.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
    }

    public static void main(String[] args) throws IOException, SftpException {
        //SyncOrganizeService ssh = new SyncOrganizeService();
        //try {
        //
        //    String config = "{\n" +
        //            "    \"contactMobile\":\"18222287231\",\n" +
        //            "    \"shellName\":\"d:/test.bat\",\n" +
        //            "    \"requestSystem\":\"cem\",\n" +
        //            "    \"sourceSystem\":\"sourceSystem\",\n" +
        //            "\t\t\"sourceTable\":\"DWS_ORG_INST_INFO_DD\",\n" +
        //            "    \"saveDir\":\"d:/data/\",\n" +
        //            "    \"separator\":\"@|@\"\n" +
        //            "}";
        //    List<String> localFileList = ssh.getLocalFileList("d:/data/");
        //    ssh.syncFileToTmpOrganize(false, localFileList);
        //    SyncOrganizeParam param = JSON.parseObject(config,SyncOrganizeParam.class);
        //    ssh.pageParseTmpOrganizeToDepartment(param);
        //} finally {
        //    ssh.closeConnect();
        //}

        String line = "slkdfj@|@sfewer";

        final String[] split = line.split("@\\|@");
        System.out.println(split);


    }


}












