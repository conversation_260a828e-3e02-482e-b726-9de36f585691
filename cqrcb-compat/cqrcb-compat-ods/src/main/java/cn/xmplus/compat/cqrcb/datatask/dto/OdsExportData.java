package cn.xmplus.compat.cqrcb.datatask.dto;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.dto.task.ResponseDetailDataDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveyResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.utils.JsonHelper;

/**
 * <AUTHOR> 导出文件的表头字段为下划线命名
 */
@Getter
@Setter
@NoArgsConstructor
public class OdsExportData {

    @Schema(description = "id")
    private Long id;
    @Schema(description = "问卷编号")
    private Long survey_id;
    @Schema(description = "问卷名称")
    private String survey_name;
    @Schema(description = "答卷编号")
    private Long sequence;
    @Schema(description = "回收类型")
    private String channel_type;
    @Schema(description = "发送名称")
    private String send_name;
    @Schema(description = "提交时间")
    private String finish_time;
    @Schema(description = "开始时间")
    private String start_time;
    @Schema(description = "答题状态")
    private ResponseStatus status;
    @Schema(description = "ip")
    private String ip;
    @Schema(description = "国家(ip)")
    private String ip_country;
    @Schema(description = "省份(ip)")
    private String ip_province;
    @Schema(description = "城市(ip)")
    private String ip_city;
    @Schema(description = "答题时长(秒)")
    private Integer duration_seconds;
    @Schema(description = "外部客户编号")
    private String euid;
    @Schema(description = "部门编号")
    private String department_code;
    @Schema(description = "部门名称")
    private String department_name;
    @Schema(description = "外部参数")
    private Map<String, Object> parameters;
    @Schema(description = "答案")
    private List<ResponseDetailDataDto> response_cell;

    public OdsExportData(Survey survey, SurveyResponse response, List<SurveyChannel> channels,
            List<ResponseDetailDataDto> data) {
        this.id = response.getId();
        this.survey_id = response.getSurveyId();
        this.survey_name = survey.getTitle();
        this.sequence = response.getSequence();
        this.channel_type = response.getCollectorMethod().getText();
        this.finish_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                .format(response.getFinishTime());
        this.start_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                .format(response.getCreateTime());
        this.status = response.getStatus();
        this.ip = response.getIp();
        this.duration_seconds = response.getDurationSeconds();
        this.ip_country = response.getCountry();
        this.ip_province = response.getProvince();
        this.ip_city = response.getCity();
        this.department_code = response.getDepartmentCode();
        this.department_name = response.getDepartmentName();
        this.euid = response.getExternalUserId();
        this.parameters = response.getParameters();
        this.response_cell = data;

        // 渠道
        channels.stream().filter(c -> c.getId().equals(response.getChannelId()))
                .map(SurveyChannel::getName).findFirst().ifPresentOrElse(x -> this.send_name = x,
                () -> this.send_name = SurveyCollectorMethod.LINK.getText());
    }

    public String[] toStringArray() {
        return Arrays.stream(OdsExportData.class.getDeclaredFields()).map(f->{
            f.setAccessible(true);
            try {
                if (f.getType()!=String.class && f.getType() != ResponseStatus.class) {
                    return JsonHelper.toJson(f.get(this));
                } else {
                    return f.get(this) == null? null : f.get(this).toString();
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            return null;
        }).toArray(String[]::new);
    }
}
