package cn.xmplus.compat.cqrcb.datatask.service;


import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.xmplus.compat.cqrcb.consts.DataTaskType;
import cn.xmplus.compat.cqrcb.dto.SyncResponseParam;
import cn.xmplus.compat.cqrcb.entity.DataTask;
import cn.xmplus.compat.cqrcb.entity.TmpImport;
import cn.xmplus.compat.cqrcb.repository.DataTaskRepository;
import cn.xmplus.compat.cqrcb.repository.TmpImportRepository;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.entity.Department;
import org.befun.auth.repository.DepartmentRepository;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.hibernate.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR>
 * @date 2023/9/25/0025 18:18:30
 * @desc
 */
@Slf4j
@Component
public class SyncSurveyResponse {


    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final int batchSize  = 500;

    @Autowired(required = false)
    private SurveyResponseRepository surveyResponseRepository;

    @Autowired
    private TmpImportRepository tmpImportRepository;

    @Autowired
    private DataTaskRepository dataTaskRepository;

    @Autowired
    private EntityManager entityManager;

    @Autowired(required = false)
    private EventRepository eventRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    /**
     * 执行shell脚本,分别拉去拉取神策事件表、人力表、机构表
     */
    public void exeShell(SyncResponseParam param) {
        List<String> shells = param.getShells();
        shells.forEach(s -> {
            Optional.ofNullable(s).ifPresent(x -> {
                String shell = getCompleteShell(s, param.getSaveDir());
                log.info("导入任务shell脚本：{}", shell);
                try {
                    Process process = Runtime.getRuntime().exec(shell);
                    int code = process.waitFor();
                    if (code == 0) {
                        log.info("执行{}成功返回码：{}", shell, code);
                    } else {
                        log.warn("执行{}失败返回码：{}", shell, code);
                        throw new BadRequestException(String.format("执行{}失败返回码：{}", shell, code));
                    }
                } catch (IOException | InterruptedException e) {
                    throw new RuntimeException(String.format("shell脚本(%s)执行出错\n%s", shell, e.getMessage()));
                }
            });
        });
    }


    public List<File> listFile(File file) {
        Objects.nonNull(file);
        if (!file.exists()) {
            log.error("{}不存在", file.getPath());
            return List.of();
        }
        List<File> res = new ArrayList<>();
        if (file.isFile()) {
            res.add(file);
        } else {
            File[] files = file.listFiles();
            for (File childFile : files) {
                List<File> list = listFile(childFile);
                res.addAll(list);
            }
        }
        return res;
    }


    public SyncResponseParam getParam() {
        DataTask dataTask = dataTaskRepository.findFirstByTaskTypeAndStatus(DataTaskType.IMPORT_DATA_TASK, 1);
        if (dataTask == null) {
            throw new RuntimeException(DataTaskType.IMPORT_DATA_TASK + "任务未启用或不存在");
        }
        return JsonHelper.toObject(dataTask.getConfig(), SyncResponseParam.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pageParseDataToSurveyResponseAndEvent(Long surveyId) {
        long minId = 0;
        boolean hasData = true;
        SyncResponseParam param = this.getParam();
        final Map<String, String> executivePersonMap = this.getExecutivePersonName(param);
        while (hasData) {
            List<TmpImport> tmpImports = tmpImportRepository.findByDateAndFileNameLikeAndIdGreaterThan(this.getDateString(), "%"+param.getShenceTable()+"%",
                    minId, PageRequest.of(0, batchSize, Sort.by("id")));
            List<TmpImport> res = new ArrayList<>();
            res.addAll(tmpImports);
            if (CollectionUtils.isNotEmpty(tmpImports)) {
                parseDataToSurveyResponseAndEvent(surveyId, tmpImports,executivePersonMap);
                minId = res.get(res.size() - 1).getId();
            } else {
                hasData = false;
            }
        }
    }

    /**
     * 将客户编号，name转换为map，用于通过编号获取name
     * @param param
     * @return
     */
    public Map<String,String> getExecutivePersonName(SyncResponseParam param){
         List<TmpImport> manPowers = tmpImportRepository.findByDateAndFileNameLike(this.getDateString(), "%"+param.getRenliTable()+"%");
         Map<String,String> res = new HashMap<>();
         manPowers.forEach(line->{
             final String data = line.getData();
             String[] split = data.split(param.getManPowerSeparator());
             if(split.length>2){
                 //客户经理编号
                 String executivePersonNo = split[0];
                 String executivePersonName = split[1];
                 res.put(executivePersonNo,executivePersonName);
             }
         });
         return res;
    }

    /**
     * 解析神策事件数据到答卷表自定义参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void parseDataToSurveyResponseAndEvent(Long surveyId,List<TmpImport> tmpImports,Map<String, String> executivePersonMap) {
        log.info("开始同步问卷id：{}", surveyId);
        int responseNum = 0;
        int eventNum = 0;
        List<SurveyResponse> responses = surveyResponseRepository.findBySurveyId(surveyId);
        final SyncResponseParam param = this.getParam();
        for (SurveyResponse response : responses) {
            Map<String, Object> parameters = response.getParameters();
            Iterator<TmpImport> iterator = tmpImports.iterator();
            while (iterator.hasNext()) {
                String eventData = iterator.next().getData();
                String[] splitData = eventData.split(param.getShenCeSeparator());
                //读取文件每一列的数据
                String idProperty = splitData[0];
                String userProperty = splitData[1];
                String eventProperty = splitData[2];

                if (existSameParameter(parameters, idProperty,response.getExternalUserId())) {
                    responseNum++;
                    //更新自定义参数到答卷表
                    Map<String, Object> resParameter = getParameters(userProperty, eventProperty,executivePersonMap);
                    resParameter.putAll(response.getParameters());
                    response.setParameters(resParameter);
                    String departmentCode = getDepartmentCode(eventProperty);
                    String departmentName = getDepartmentNameByCode(response.getOrgId(), departmentCode);
                    response.setDepartmentCode(departmentCode);
                    response.setDepartmentName(departmentName);
                    surveyResponseRepository.save(response);
                    //更新事件表
                    Optional<Event> event = eventRepository.findOneBySurveyIdAndResponseId(response.getSurveyId(), response.getId());
                    if (event.isPresent()) {
                        eventNum++;
                        Event eventResult = event.get();
                        eventResult.setParameters((HashMap) resParameter);
                        eventResult.setDepartmentName(departmentName);
                        eventRepository.save(eventResult);
                    }
                    iterator.remove();
                    break;//匹配到了结束循环
                }
            }

        }
        log.info("同步问卷id结束：{},答卷数量：{}，事件数量：{}", surveyId, responseNum, eventNum);
    }

    /**
     * @param eventProperty
     * [
     *        {
     *        "event_id":"11",
     *        "event":"buy",
     *        "time":"16993453453",
     *        "xxx1":"xxxx"
     *        },
     *        {
     *        "event_id":"11",
     *        "event":"return",
     *        "time":"16993453453",
     *        "xxx1":"xxxx"
     *        }
     * ]
     */
    public String getDepartmentCode(String eventProperty) {
        try {
            List<Map> eventPropertyMapList = JSON.parseArray(eventProperty, Map.class);
            for (Map<String, Object> eventPropertyMap : eventPropertyMapList) {
                Object departmentCode = eventPropertyMap.get("executive_institution_no");
                if(departmentCode != null){
                    return departmentCode.toString();
                }
            }
            return StringUtils.EMPTY;
        }catch (Exception e){
            log.error("获取部门编码异常：{}",e.getMessage());
            return StringUtils.EMPTY;
        }
    }

    /**
     * 根据部门编码获取名称
     * @param orgId
     * @param departmentCode
     * @return
     */
    public String getDepartmentNameByCode(Long orgId, String departmentCode) {
        try {
            Department department = departmentRepository.findByOrgIdAndCode(orgId, departmentCode);
            if(department == null){
                return StringUtils.EMPTY;
            }
            return department.getTitle();
        }catch (Exception e){
            log.error("获取部门名称异常：{}",e.getMessage());
            return StringUtils.EMPTY;
        }
    }

    /**
     * 判断自定义参数是否有相同的运营计划ID、客户编号、发送日期
     * @param parameter response 自定义参数
     * @param idProperty 运营计划ID_客户编号_发送日期
     * @param externalUserId 客户编号
     * @return
     */
    public boolean existSameParameter(Map<String, Object> parameter, String idProperty,String externalUserId) {
        if (ObjectUtils.isEmpty(parameter) || StringUtils.isEmpty(idProperty)) {
            return false;
        }
        //param=运营计划ID_客户编号_发送日期
        String[] params = idProperty.split("_");

         String planId = params[0];
         String euid = params[1];
         String sendTime = params[2];

         if(parameter.get("planId") == null || parameter.get("sendTime") == null || externalUserId == null){
             return false;
         }

         if(planId.equals(parameter.get("planId").toString()) &&
                 euid.equals(externalUserId) &&
                 sendTime.trim().equals(parameter.get("sendTime"))){
             return true;
         }
        return false;
    }


    /**
     *获取自定义参数
     * @param userProperty
     * {
     *        "age":"11",
     *        "name":"name",
     *        "xxx":"xxx"
     *  }
     * @param eventProperty
     * [
     *        {
     *        "event_id":"11",
     *        "event":"buy",
     *        "time":"16993453453",
     *        "xxx1":"xxxx"
     *        },
     *        {
     *        "event_id":"11",
     *        "event":"return",
     *        "time":"16993453453",
     *        "xxx1":"xxxx"
     *        }
     * ]
     * @return
     * {
     * "age":"11",//客户属性
     * "name":"xxx",//客户属性
     * "buy_xxx1":"xxx",//buy事件的属性1
     * 	"buy_xxx2":"xxx",//buy事件的属性2
     * 	"return_xxx1":"xxx",//return事件的属性1
     * 	”executive_person_name“:"xxx"
     * }
     */
    private Map<String, Object> getParameters(String userProperty, String eventProperty,Map<String, String> executivePersonMap) {
        Map<String, Object> userPropertyMap = JsonHelper.toMap(userProperty);

        Map<String, Object> parameters = new HashMap<>();

        if (userPropertyMap != null) {
            parameters.putAll(userPropertyMap);
        }

        List<Map> eventPropertyMapList = JSON.parseArray(eventProperty, Map.class);

        if (eventPropertyMapList != null && eventPropertyMapList.size() > 0) {
            //不需要的key
            List<String> excludeKey = List.of("event_id", "time", "event");
            for (Map<String, Object> eventPropertyMap : eventPropertyMapList) {
                //拼接参数key
                String event = eventPropertyMap.get("event").toString();
                eventPropertyMap.forEach((k, v) -> {
                    if (!excludeKey.contains(k)) {
                        String key = event + "_" + k;
                        parameters.put(key, v);
                        //通过人力表的编号查找姓名
                        if("executive_person_no".equals(k)){
                            parameters.put("executive_person_name",executivePersonMap.get(v));
                        }
                        if ("issue_employee_no".equals(k)) {
                            parameters.put("issue_employee_name", executivePersonMap.get(v));
                        }
                    }
                });
            }
        }
        return parameters;
    }

    /**
     *  拼接完整的shell脚本 sh /a.sh cem dws %s table %s
     * @param shell
     * @param saveDir 日期
     * @return
     */
    public String getCompleteShell(String shell, String saveDir) {
        String res = String.format(shell,getDateString(-1),this.parseSaveDir(saveDir));
        return res;
    }


    /**
     * 拼接完整的shell脚本  /data/task.sh p1 p2
     *
     * @param sourceTable
     * @return
     */
    public String getCompleteShell(String shellName, String requestSystem, String sourceSystem, String sourceTable, String saveDir, String environment) {
        StringJoiner shell = new StringJoiner(" ");
        shell.add("sh");
        shell.add(shellName);
        shell.add(requestSystem);
        shell.add(sourceSystem);
        shell.add(getDateString(-1));
        shell.add(sourceTable);
        shell.add(this.parseSaveDir(saveDir));
        shell.add(environment);
        return shell.toString();
    }

    /**
     * 保存文件的目录
     *
     * @return
     */
    public String parseSaveDir(String saveDir) {
        String dateString = getDateString(-1);
        if (saveDir.endsWith("/")) {
            saveDir = saveDir + dateString;
        } else {
            saveDir = saveDir + "/" + dateString;
        }
        File create = new File(saveDir);
        if (!create.exists()) {
            create.mkdirs();
        }
        return saveDir;
    }

    /**
     * 日期字符串
     *
     * @param addDay 0当天，-1昨天，1明天
     * @return 20230927
     */
    public String getDateString(int addDay) {
        return LocalDate.now().plusDays(addDay).format(formatter);
    }

    public String getDateString() {
        return this.getDateString(0);
    }
    /**
     * 将file中每一行的数据解析到tmp_import数据库中
     *
     * @param file
     */
    @Transactional(rollbackFor = Exception.class)
    public void parseFileToDB(File file) {
        if(existsByDateAndFileName(file.getName())){
            log.info("{}文件在数据库中已存在",file.getName());
            return;
        }
        List<TmpImport> tmpImports = new ArrayList<>();
        readFile(file, line -> {
            //拉取的文件数据保存到临时表中
            TmpImport tmpImport = new TmpImport();
            tmpImport.setDate(this.getDateString());
            tmpImport.setData(line);
            tmpImport.setTaskType(1);
            tmpImport.setFileName(file.getName());
            tmpImports.add(tmpImport);
            if (tmpImports.size() == 50) {
                tmpImportRepository.saveAll(tmpImports);
                tmpImports.clear();
            }
        });
        tmpImportRepository.saveAll(tmpImports);
    }

    public boolean existsByDateAndFileName(String filename){
        String date = this.getDateString();
        return tmpImportRepository.existsByDateAndFileName(date,filename);
    }

    public <T> void batchSave(List<T> entities) {
        Session session = entityManager.unwrap(Session.class);
        for (int i = 0; i < entities.size(); i++) {
            session.save(entities.get(i));
            if (i % 100 == 0 && i > 0) {
                session.flush();
                session.clear();
            }
        }
    }

    /**
     * 读取文件数据
     *
     * @param file     文件
     * @param consumer 操作读取的每行数据
     */
    public void readFile(File file, Consumer<String> consumer) {
        try (
                GZIPInputStream gzipInputStream = new GZIPInputStream(new FileInputStream(file));
                Reader reader = new InputStreamReader(gzipInputStream, StandardCharsets.UTF_8);
                BufferedReader bufferedReader = new BufferedReader(reader);
        ) {
            String line = "";
            boolean skipFirstLine = this.getParam().isSkipFirstLine();
            while ((line = bufferedReader.readLine()) != null) {
                if(skipFirstLine){
                    skipFirstLine = false;
                    continue;
                }
                if(!line.trim().isEmpty()){
                    consumer.accept(line);
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("读取文件错误：" + e.getMessage());
        }
    }

    /**
     * 同步答卷表的外参到事件表的外参
     */
    public void syncParameterByResponseToEvent(List<Long> sids) {
        sids.forEach(sid -> {
            List<SurveyResponse> surveyResponses = surveyResponseRepository.findBySurveyId(sid);
        });
    }

    public void clearOldData() {
        log.info("清除旧数据,date:{}", getDateString(-3));
        try {
            List<TmpImport> tmpImports = tmpImportRepository.findAllByDateLessThan(getDateString(-3));
            tmpImports.forEach(t -> tmpImportRepository.delete(t));
        } catch (Exception ex) {
            log.warn("清除旧数据失败，caused by :{}", ex.getMessage());
        }
    }

    public static void main(String[] args) throws IOException {
        SyncSurveyResponse export = new SyncSurveyResponse();
        //export.readFile("d:/a.txt", line -> {
        //    System.out.println(line);
        //});

/*        try {

            Process process = Runtime.getRuntime().exec(" cmd /c d:/test.bat");

            // 获取脚本输出流
            InputStream inputStream = process.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));

            // 读取脚本输出
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }
            System.out.println(process.waitFor());
            System.out.println(11111);
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException("shell脚本" + "执行出错\n" + e.getMessage());
        }*/

        String data = "{\n" +
                "\"shellName\":\"a.sh\",\n" +
                "\"requestSystem\":\"cem\",\n" +
                "\"sourceSystem\":\"sourceSystem\",\n" +
                "\"sourceTables\":[\"table\"],\n" +
                "\"date\":\"20220203\",\n" +
                "\"saveDir\":\"d:/data/\",\n" +
                "\"separator\":\"@\"\n" +
                "}";
        String data2 = "{\n" +
                "\"shellName\":\"a.sh\",\n" +
                "\"requestSystem\":\"cem\",\n" +
                "\"sourceSystem\":\"sourceSystem\",\n" +
                "\"sourceTables\":[\"table\"],\n" +
                "\"date\":\"20220203\",\n" +
                "\"saveDir\":\"d:/data/\",\n" +
                "\"separator\":\"@\"\n" +
                "}";

        ObjectMapper objectMapper = new ObjectMapper();
        TypeFactory typeFactory = objectMapper.getTypeFactory();
        MapType mapType = typeFactory.constructMapType(HashMap.class, String.class, Object.class);

        Map<String, Object> map = objectMapper.readValue(data, mapType);
        Map<String, Object> map2 = objectMapper.readValue(data2, mapType);

        System.out.println(map.equals(map2));


    }
}



























