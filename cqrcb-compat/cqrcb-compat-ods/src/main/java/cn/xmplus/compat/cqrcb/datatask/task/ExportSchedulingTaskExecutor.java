package cn.xmplus.compat.cqrcb.datatask.task;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.service.ChannelService;
import cn.xmplus.compat.cqrcb.consts.DataTaskLogStatus;
import cn.xmplus.compat.cqrcb.consts.DataTaskType;
import cn.xmplus.compat.cqrcb.datatask.dto.OdsExportContext;
import cn.xmplus.compat.cqrcb.datatask.dto.OdsExportData;
import cn.xmplus.compat.cqrcb.datatask.service.ResponseProcessService;
import cn.xmplus.compat.cqrcb.datatask.utils.OdsExport;
import cn.xmplus.compat.cqrcb.dto.OdsExportConfig;
import cn.xmplus.compat.cqrcb.entity.DataTask;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.nativesql.SqlBuilder;
import org.befun.extension.service.NativeSqlHelper;
import org.befun.task.annotation.TaskLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("exportSchedulingTask")
public class ExportSchedulingTaskExecutor extends SchedulingTaskExecutor {

    @Autowired
    private ResponseProcessService responseProcessService;

    @Autowired
    ChannelService channelService;

    @Autowired
    private NativeSqlHelper nativeSqlHelper;

    private static final int LIMIT = 200;
    private static final int MINUS_DAY = 1;

    @Override
    public DataTaskType type() {
        return DataTaskType.EXPORT_DATA_TASK;
    }

    @Override
    public boolean isvalidConfig() {
        OdsExportConfig config = (OdsExportConfig) getConfig();
        if (dataTask.getCron().isBlank()) {
            log.error("定时任务：{}，无效的cron表达式：{}", this.dataTask.getId(), dataTask.getCron());
            return false;
        }
        if (config.getContactMobile().isEmpty()) {
            log.error("定时任务：{}，无效的紧急联系人：{}", this.dataTask.getId(), config.getContactMobile());
            return false;
        }
        return true;
    }

    @Override
    public boolean diffConfig(DataTask dataTask) {
        if (dataTask.getTaskType() != this.dataTask.getTaskType() || !dataTask.getStatus()
                .equals(this.dataTask.getStatus())) {
            return true;
        }
        String version = JsonHelper.toMap(dataTask.getConfig()).getOrDefault("version", 0)
                .toString();
        String runningVersion = JsonHelper.toMap(getConfig()).getOrDefault("version", 0).toString();

        return !version.equals(runningVersion);
    }

    @Override
    @TaskLock(key = "export-task")
    public void task() {
        OdsExportConfig config = (OdsExportConfig) getConfig();
        OdsExportContext context = new OdsExportContext();
        context.fillFileName(LocalDate.now().minusDays(MINUS_DAY).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        context.setBasePath(config.getLocalPath());
        context.setTotal(countSurveyResponse(config));
        OdsExport export = new OdsExport(context, config);
        if (config.getSurveyIds().isEmpty()) {
            log.info("配置的导出问卷id为空");
            updateTaskLogStatus(DataTaskLogStatus.SUCCESSED, "配置的导出问卷id为空");
            return;
        }
        try {
            for (Long surveyId : config.getSurveyIds()) {
                // 分批组装答卷数据
                Survey survey = responseProcessService.getSurvey(surveyId);
                List<SurveyChannel> channelList = channelService.getChannelList(surveyId);
                boolean hasNext = true;
                int offset = 0;
                do {
                    String sql = String
                            .format("select id from survey_response where s_id = (%s) and is_completed = true and (status = %s or status = %s) and finish_time between '%s' and '%s' limit %s offset %s",
                                    survey.getId(),
                                    ResponseStatus.FINAL_SUBMIT.ordinal(),
                                    ResponseStatus.INVALID.ordinal(),
                                    LocalDateTime.of(LocalDate.now().minusDays(MINUS_DAY),
                                            LocalTime.MIN),
                                    LocalDateTime.of(LocalDate.now(),
                                            LocalTime.MIN),
                                    LIMIT,
                                    offset);
                    List<Long> responseIds = nativeSqlHelper.queryIds(SqlBuilder.select(sql));
                    if (responseIds.isEmpty() && offset != 0) {
                        hasNext = false;
                        continue;
                    }
                    TenantContext.setCurrentTenant(survey.getOrgId());
                    List<OdsExportData> detailDtos = this.responseProcessService
                            .processResponse(survey, channelList, responseIds);
                    // 写入本地文件
                    export.writeDataFile(detailDtos);
                    offset += LIMIT;
                    TenantContext.clear();
                    updateTaskLogStatus(context.getStatus(), context.jsonResult());
                } while (hasNext);
            }
            // 生成压缩文件并导出至本地
            if (context.getDataFile() != null) {
                File packageFile = export.packageFile();
                // 上传至ods
                if (config.isNeedConnect()) {
                    boolean uploadSuccess = export.upload(packageFile);
                    if (!uploadSuccess) {
                        sendMessage(config.getContactMobile(), config.getContactContent(),
                                JsonHelper.toMap(config));
                        context.setStatus(DataTaskLogStatus.FAILED);
                        context.getResult().getReasons().add("上传文件失败");
                    }
                }
            }
            export.finishExport();
        } catch (Exception ex) {
            TenantContext.clear();
            ex.printStackTrace();
            context.setStatus(DataTaskLogStatus.FAILED);
            context.getResult().getReasons().add(ex.getMessage());
        } finally {
            updateTaskLogStatus(context.getStatus(), context.jsonResult());
        }
    }

    private long countSurveyResponse(OdsExportConfig config) {
        if (config.getSurveyIds().isEmpty()) {
            return 0;
        }
        String sql = String
                .format("select count(id) from survey_response where s_id in (%s) and status = %s and is_completed = true and finish_time between '%s' and '%s'",
                        StringUtils.join(config.getSurveyIds(), ","),
                        ResponseStatus.FINAL_SUBMIT.ordinal(),
                        LocalDateTime.of(LocalDate.now().minusDays(MINUS_DAY),
                                LocalTime.MIN), LocalDateTime.of(LocalDate.now(),
                                LocalTime.MIN));
        return nativeSqlHelper.count(sql);
    }
}
