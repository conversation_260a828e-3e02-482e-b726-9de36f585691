package cn.xmplus.compat.cqrcb.datatask.task;


import cn.hutool.core.lang.Assert;
import cn.xmplus.compat.cqrcb.consts.DataTaskType;
import cn.xmplus.compat.cqrcb.datatask.service.SyncOrganizeService;
import cn.xmplus.compat.cqrcb.dto.SyncOrganizeParam;
import cn.xmplus.compat.cqrcb.dto.SyncResponseParam;
import cn.xmplus.compat.cqrcb.entity.DataTask;
import cn.xmplus.compat.cqrcb.entity.TmpOrganize;
import cn.xmplus.compat.cqrcb.repository.DataTaskRepository;
import cn.xmplus.compat.cqrcb.repository.TmpOrganizeRepository;
import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.entity.Department;
import org.befun.auth.repository.DepartmentRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.task.annotation.TaskLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR>
 * @date 2023/6/9 13:43:06
 */

@Slf4j
@Component
public class SyncOrganize extends SchedulingTaskExecutor {

    @Autowired
    private SyncOrganizeService syncOrganizeService;

    @Override
    public DataTaskType type() {
        return DataTaskType.SYNC_ORGANIZE_TASK;
    }

    @Override
    @TaskLock(key = "sync-organize")
    public void task() {
        SyncOrganizeParam config = syncOrganizeService.getParam();
        if(config.isRemote()){
            syncOrganizeService.syncOrganizeRemote(config);
        }else {
            syncOrganizeService.syncOrganizeLocal(config);
        }
        this.writeLog(true, "sync-organize Success");
    }

    @Override
    public boolean isvalidConfig() {
        SyncOrganizeParam config = (SyncOrganizeParam) this.getConfig();
        if (config.getContactMobile().isEmpty()) {
            log.error("定时任务：{}，无效的紧急联系人：{}", this.dataTask.getId(), config.getContactMobile());
            return false;
        }
        return true;
    }

    @Override
    public boolean diffConfig(DataTask datatask) {
        return true;
    }
}












