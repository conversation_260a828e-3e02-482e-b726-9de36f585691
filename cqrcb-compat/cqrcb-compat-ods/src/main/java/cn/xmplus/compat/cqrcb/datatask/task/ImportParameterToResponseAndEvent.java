package cn.xmplus.compat.cqrcb.datatask.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.xmplus.compat.cqrcb.consts.DataTaskLogStatus;
import cn.xmplus.compat.cqrcb.consts.DataTaskType;
import cn.xmplus.compat.cqrcb.datatask.service.SyncSurveyResponse;
import cn.xmplus.compat.cqrcb.dto.SyncResponseParam;
import cn.xmplus.compat.cqrcb.entity.DataTask;
import cn.xmplus.compat.cqrcb.entity.DataTaskLog;
import cn.xmplus.compat.cqrcb.repository.DataTaskLogRepository;
import cn.xmplus.compat.cqrcb.repository.TmpImportRepository;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.utils.JsonHelper;
import org.befun.task.annotation.TaskLock;
import org.befun.task.constant.TaskStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/2/0002 22:38:14
 * @desc t+1天执行shell脚本，拉取神策事件表、人力表、机构表
 */
@Component
@Slf4j
public class ImportParameterToResponseAndEvent extends SchedulingTaskExecutor {

    @Autowired
    private SyncSurveyResponse syncSurveyResponse;

    @Autowired
    private DataTaskLogRepository dataTaskLogRepository;


    @Override
    public DataTaskType type() {
        return DataTaskType.IMPORT_DATA_TASK;
    }

    @Override
    @TaskLock(key = "import-task")
    public void task() {
        log.info("===========开始执行"+DataTaskType.IMPORT_DATA_TASK+"============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("执行shell");
        //执行shell
        SyncResponseParam param = syncSurveyResponse.getParam();
        syncSurveyResponse.exeShell(param);
        stopWatch.stop();

        stopWatch.start("同步神策数据");
        String path = syncSurveyResponse.parseSaveDir(param.getSaveDir());
        List<File> fileList = syncSurveyResponse.listFile(new File(path))
                .stream().filter(x-> x.getName().contains(param.getShenceTable()) || x.getName().contains(param.getRenliTable()))
                .collect(Collectors.toList());;
        if(CollectionUtils.isEmpty(fileList)){
            log.warn("没有拉取到文件");
            return;
        }
        log.info("path:{},fileList:{}",path,fileList.stream().map(File::getName).collect(Collectors.toList()));
        //同步神策数据到tmp_import表
        for (File file : fileList) {
            if(!file.getName().startsWith("check")){
                syncSurveyResponse.parseFileToDB(file);
            }
        }
        stopWatch.stop();
        //更新自定义参数到答卷表,同步自定义参数到事件表
        stopWatch.start("同步自定义参数");
        log.info("问卷id：{}", param.getSurveyIds());
        Optional.ofNullable(param.getSurveyIds()).ifPresent(x->{
            x.forEach(sid->{
                syncSurveyResponse.pageParseDataToSurveyResponseAndEvent(sid);
            });
        });
        stopWatch.stop();
        log.info("===========执行"+DataTaskType.IMPORT_DATA_TASK+"结束============");
        log.info(JsonHelper.toJson(stopWatch.getTaskInfo()));
        syncSurveyResponse.clearOldData();
        clearOldFile(param);
        this.writeLog(true, "Import Success");
    }

    @Override
    public boolean isvalidConfig() {
        SyncResponseParam config =  (SyncResponseParam)getConfig();
        if (config.getContactMobile().isEmpty()) {
            log.error("定时任务：{}，无效的紧急联系人：{}", this.dataTask.getId(), config.getContactMobile());
            return false;
        }
        return true;
    }

    @Override
    public boolean diffConfig(DataTask datatask) {
        if (dataTask.getTaskType() != this.dataTask.getTaskType()
                || !dataTask.getStatus().equals(this.dataTask.getStatus())) {
            return true;
        }
        final Map<String,Object> config = JsonHelper.toObject(dataTask.getConfig(), Map.class);
        final Map<String, Object> runningConfig = JsonHelper.toMap(getConfig());

        return config != runningConfig;
    }

    public void clearOldFile(SyncResponseParam param) {
        // 文件保留三天清除
        try (Stream<Path> walk = Files.walk(Paths.get(param.getSaveDir()))) {
            walk.filter(path -> path.toFile().isDirectory() && path.getFileName().toString()
                        .matches(param.getFilePattern()))
                .filter(path -> {
                    try {
                        BasicFileAttributes attrs = Files
                                .readAttributes(path, BasicFileAttributes.class);
                        LocalDate lastModified = attrs.lastModifiedTime().toInstant()
                                .atZone(
                                        ZoneId.systemDefault()).toLocalDate();
                        return lastModified.isBefore(LocalDate.now().minusDays(
                                param.getFileRetentionPeriod()));
                    } catch (Exception e) {
                        e.printStackTrace();
                        return false;
                    }})
                .forEach(path -> {
                    if (path.toFile().delete()) {
                        log.info("清除文件,{}", path.toFile().getName());
                    }
                });
        } catch (IOException ex) {
            log.warn("清除文件失败, caused by: {}", ex.getMessage());
        }
    }


    public static void main(String[] args) {
        List<Long> list = new ArrayList<>();
        list.add(1L);
        list.add(2L);
        list.add(3L);
        list.add(4L);
        try {
            for (int i = 0; i < list.size(); i++) {
                if(list.get(i)<2){
                    throw new RuntimeException("执行错误");
                }
                System.out.println("执行成功");
            }
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
    }
}
