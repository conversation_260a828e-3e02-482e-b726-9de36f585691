CREATE TABLE `project_cqrcb_credit_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL COMMENT '企业id',
  `push_id` bigint DEFAULT NULL COMMENT '数据接入id',
  `serial_number` varchar(64) DEFAULT NULL COMMENT '流水号',
  `euid` varchar(128) DEFAULT NULL COMMENT '客户编号',
  `send_status` varchar(20) DEFAULT NULL COMMENT '发送状态',
  `send_times` int COMMENT '发送次数',
  `request` text COMMENT '请求参数',
  `response` text COMMENT '响应参数',
  `is_complete` tinyint DEFAULT '0' COMMENT '是否完成',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_push_id` (`push_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='积分记录';