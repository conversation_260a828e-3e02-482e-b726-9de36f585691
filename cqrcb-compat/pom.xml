<?xml version="1.0"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.hanyi.compat</groupId>
        <artifactId>project-compat</artifactId>
        <version>${compat.version}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>cqrcb-compat</artifactId>
    <version>${cqrcb-compat.version}</version>
    <packaging>pom</packaging>
    <name>cqrcb-compat</name>

    <properties>
        <core.version>1.9.8.203-CQRCB</core.version>
        <ctm.version>1.9.8.307-CQRCB</ctm.version>
        <auth.version>1.9.8.300-CQRCB</auth.version>
        <survey.version>1.9.8.300-CQRCB</survey.version>
        <worker.version>1.9.8.300-CQRCB</worker.version>
        <cqrcb-compat.version>1.0.312-CQRCB</cqrcb-compat.version>
    </properties>

    <modules>
        <module>cqrcb-compat-common</module>
        <module>cqrcb-compat-credit</module>
        <module>cqrcb-compat-data-access</module>
        <module>cqrcb-compat-ods</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.befun.task</groupId>
                <artifactId>befun-task</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun</groupId>
                <artifactId>befun-core</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.extension</groupId>
                <artifactId>befun-x-pack</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.auth</groupId>
                <artifactId>befun-auth-core</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.auth</groupId>
                <artifactId>befun-auth-pay</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.auth</groupId>
                <artifactId>befun-auth-trigger</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.bi</groupId>
                <artifactId>bi-base</artifactId>
                <version>${bi.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-customer-core</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-data-core</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-common-core</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-event-core</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-journey-core</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-trigger</artifactId>
                <version>${ctm.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>survey-base</artifactId>
                <version>${survey.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <version>${survey.version}</version>
                <artifactId>survey-core</artifactId>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>survey-client</artifactId>
                <version>${survey.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>survey-trigger</artifactId>
                <version>${survey.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>cem-core</artifactId>
                <version>${worker.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>cem-compat</artifactId>
                <version>${worker.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi.compat</groupId>
                <artifactId>cqrcb-compat-common</artifactId>
                <version>${cqrcb-compat.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi.compat</groupId>
                <artifactId>cqrcb-compat-credit</artifactId>
                <version>${cqrcb-compat.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi.compat</groupId>
                <artifactId>cqrcb-compat-data-access</artifactId>
                <version>${cqrcb-compat.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi.compat</groupId>
                <artifactId>cqrcb-compat-ods</artifactId>
                <version>${cqrcb-compat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>2.3.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
