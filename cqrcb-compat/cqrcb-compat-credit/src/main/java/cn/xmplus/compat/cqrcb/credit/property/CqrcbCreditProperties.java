package cn.xmplus.compat.cqrcb.credit.property;

import cn.xmplus.compat.cqrcb.credit.CqrcbCompatCreditConfig;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = CqrcbCompatCreditConfig.CREDIT)
public class CqrcbCreditProperties {

    private String urlSuffix;
    private int checkSize = 10;
    private CqrcbCreditScheduling scheduling;
    private String checkUrl;
    private String contactMobile;
    private String outNumberMessage;
    private String taskLockMessage;
    private int warningSize = 1000;

    @Getter
    @Setter
    public static class CqrcbCreditScheduling {
        private boolean enabled;
        private String cron;
    }
}
