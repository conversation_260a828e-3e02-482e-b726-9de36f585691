package cn.xmplus.compat.cqrcb.credit.scheduling;

import cn.xmplus.compat.cqrcb.credit.helper.CqrcbCreditHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.*;
import java.util.Date;
import java.util.Optional;

import static cn.xmplus.compat.cqrcb.credit.CqrcbCompatCreditConfig.SCHEDULING;

@Slf4j
public class CqrcbCreditScheduling {

    @Autowired
    private CqrcbCreditHelper helper;
    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String LOCK_KEY = SCHEDULING + ".lock";

    @Scheduled(cron = "${project-compat.cqrcb.credit.scheduling.cron}")
    public void scheduling() {

        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime startTime = currentTime.minusDays(10);

        // 将 LocalDateTime 转换为时间戳
        Instant instant = currentTime.atZone(ZoneId.systemDefault()).toInstant();
        long timestamp = instant.toEpochMilli();

        log.info("start to check credit record, time = {}", LocalDateTime.now());

        boolean lock = Optional.ofNullable(redisTemplate.opsForValue().setIfAbsent(LOCK_KEY, Long.toString(timestamp))).orElse(false);
        if (!lock) {
            //锁的时间 第二天还没释放 发短信预警 并且释放锁
            log.info("积分对账任务正在执行");
            return;
        }
        try {
            //对账
            helper.check(startTime, currentTime);
            //重发
            helper.resend(startTime, currentTime);
        } catch (Throwable e) {
            log.error("CqrcbCreditScheduling error", e);
        } finally {
            redisTemplate.delete(LOCK_KEY);
            log.info("end to check credit record, time = {}", LocalDateTime.now());
        }
    }
}
