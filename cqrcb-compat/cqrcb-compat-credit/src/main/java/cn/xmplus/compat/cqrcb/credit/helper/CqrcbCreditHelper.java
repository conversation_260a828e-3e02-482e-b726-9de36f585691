package cn.xmplus.compat.cqrcb.credit.helper;

import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskWebHookDto;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.ctm.entity.Push;
import cn.hanyi.survey.core.dto.task.ResponseDetailDto;
import cn.hutool.core.map.MapUtil;
import cn.xmplus.compat.cqrcb.credit.consts.CreditStatus;
import cn.xmplus.compat.cqrcb.credit.dto.CreditBatchQueryDataDto;
import cn.xmplus.compat.cqrcb.credit.dto.CreditBatchQueryResponseDto;
import cn.xmplus.compat.cqrcb.credit.property.CqrcbCreditProperties;
import cn.xmplus.compat.cqrcb.credit.webhook.CreditWebhookCompatInfo;
import cn.xmplus.compat.cqrcb.dto.SimpleCreditRecordDto;
import cn.xmplus.compat.cqrcb.entity.ProjectCqrcbCreditRecord;
import cn.xmplus.compat.cqrcb.repository.ProjectCqrcbCreditRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpVersion;
import org.apache.http.client.fluent.Content;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.apache.http.protocol.HTTP;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.SmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CqrcbCreditHelper {

    @Autowired
    private TaskProducerHelper taskProducerHelper;
    @Autowired
    private CqrcbCreditProperties properties;
    @Autowired
    private ProjectCqrcbCreditRecordRepository repository;
    @Autowired
    private CqrcbEsbXmlHelper esbXmlHelper;
    @Autowired
    private SmsService smsService;

    public CreditWebhookCompatInfo buildInfo(Push push) {
        String url;
        if (push == null || StringUtils.isEmpty(properties.getUrlSuffix()) || StringUtils.isEmpty(url = push.getAddress())) {
            return null;
        }
        // 校验网关后缀是否匹配
        if (!url.contains(properties.getUrlSuffix())) {
            return null;
        }
        ResponseDetailDto pushDetail = JsonHelper.toObject(push.getContent(), ResponseDetailDto.class);
        if (pushDetail == null) {
            return null;
        }
        // 尝试使用 pushId 查询，重发的时候，记录已经存在了
        ProjectCqrcbCreditRecord record = repository.findFirstByOrgIdAndPushId(push.orgId, push.getId());

        String serialNumber = esbXmlHelper.createSerialNumber();
        pushDetail.getParameters().put("serialNumber",serialNumber);
        pushDetail.getParameters().put("pushId",push.getId());
        push.setContent(JsonHelper.toJson(pushDetail));

        ProjectCqrcbCreditRecord newRecord = new ProjectCqrcbCreditRecord();
        newRecord.setOrgId(push.getOrgId());
        newRecord.setPushId(push.getId());
        newRecord.setSerialNumber(serialNumber); //流水号
        newRecord.setEuid(pushDetail.getExternalUserId()); //客户编号
        newRecord.setRequest(push.getContent());
        newRecord.setSendStatus(CreditStatus.UNKNOWN.name());

        // 新增一条记录
        if (record == null) {
            newRecord.setIsComplete(false);
        } else {
            //旧记录标记已完成
            record.setIsComplete(true);
            repository.save(record);
            //补发也新增记录
            newRecord.setSendTimes(record.getSendTimes() + 1);
            newRecord.setIsComplete(false);
        }
        repository.save(newRecord);
        return new CreditWebhookCompatInfo(newRecord, pushDetail);
    }

    /**
     * *解析发送积分esb请求的xml响应内容
     * @param push
     * @param info
     * @param response
     * @return
     */
    public CreditStatus parseResponse(Push push, CreditWebhookCompatInfo info, String response) {
        log.info("compat send credit response: {}", response);
        CreditStatus status = CreditStatus.UNKNOWN;
        if(StringUtils.isEmpty(response)) return status;
        Map responseMap = esbXmlHelper.getEsbResponseBody(response);
        if (responseMap == null) return status;
        //responseBody里面只有一个 SealData json字符串
        String sealData = (String) responseMap.getOrDefault("SealData","");
        if(StringUtils.isEmpty(sealData)) return status;

        Map sealDataMap = JsonHelper.toMap(sealData);

        String code = (String) sealDataMap.getOrDefault("code","");
        //000000 操作成功 非000000 操作失败
        if(StringUtils.isNotEmpty(code) && "000000".equals(code)) {

            Map data = (Map) sealDataMap.getOrDefault("data", Map.of());
            if(data == null || data.size() == 0 ) return status;

            //grantStatus 0发放中 1发放成功 2发放失败
            String grantStatus = (String) data.getOrDefault("grantStatus","");
            return convertStatus(grantStatus);
        }else if(StringUtils.isNotEmpty(code) && !"000000".equals(code)) {
            //发放失败
            return CreditStatus.FAILED;
        } else {
            return CreditStatus.UNKNOWN;
        }
    }

    private CreditStatus convertStatus(String grantStatus) {
        CreditStatus status = CreditStatus.UNKNOWN;
        switch(grantStatus) {
            case "0":
                status = CreditStatus.SENDING;break;
            case "1":
                status = CreditStatus.SUCCESS;break;
            case "2":
                status = CreditStatus.FAILED;break;
            default:
        }
        return status;
    }

    /**
     * * 解析批量查询的结果
     * @param response
     * @return
     */
    public Map<String, CreditBatchQueryDataDto> parseQueryResponse(String response) {
        log.info("compat check credit response: {}", response);
        if(StringUtils.isEmpty(response)) return null;
        try {
            Map responseMap = esbXmlHelper.getEsbResponseBody(response);
            if (responseMap == null || responseMap.size() == 0) return null;
            //responseBody里面只有一个 SealData json字符串
            String sealData = (String) responseMap.getOrDefault("SealData","");
            if(StringUtils.isEmpty(sealData)) return null;

            CreditBatchQueryResponseDto responseDto = JsonHelper.toObject(sealData, CreditBatchQueryResponseDto.class);
            if(!"000000".equals(responseDto.getCode()) || responseDto == null) return null;

            Map<String, CreditBatchQueryDataDto> resultMap = new HashMap<>();

            for (CreditBatchQueryDataDto dto : responseDto.getData()) {
                String platformSeqNo = dto.getPlatformSeqNo();
                resultMap.put(platformSeqNo, dto);
            }
            return resultMap;
        } catch (Exception e) {
            return null;
        }
    }

    // 发送失败通知管理员
    public void creditFailure(String type) {
        switch(type) {
            case "outNumber":
                sendMessage(properties.getContactMobile(), String.format(properties.getOutNumberMessage(),properties.getWarningSize()), MapUtil.empty());
                break;
            case "taskLock":
                sendMessage(properties.getContactMobile(), properties.getTaskLockMessage(), MapUtil.empty());
                break;
            default:
        }
    }

    protected void sendMessage(String mobile, String content, Map<String, Object> param) {
        smsService.sendMessageByText(mobile, content, param);
    }

    /**
     * *对账
     * @param startTime
     * @param currentTime
     * @throws Exception
     */
    public void check(LocalDateTime startTime, LocalDateTime currentTime) throws Exception {
        Date start = DateHelper.toDate(startTime);
        Date end = DateHelper.toDate(currentTime);

        long count = repository.countBySendStatusIsInAndIsCompleteAndSendTimesAndCreateTimeBetween(
                List.of(CreditStatus.SENDING.name(),CreditStatus.UNKNOWN.name()), false, 1, start, end);

        int page = 0;
        boolean hasNext = true;
        while (hasNext) {
            //发送中 才需要去对账重发 成功的不需要对账重发
            List<ProjectCqrcbCreditRecord> records = repository.findBySendStatusIsInAndIsCompleteAndSendTimesAndCreateTimeBetween(
                    List.of(CreditStatus.SENDING.name(),CreditStatus.UNKNOWN.name()), false, 1, start, end, PageRequest.of(page, properties.getCheckSize())
            );
            if (CollectionUtils.isNotEmpty(records) && records.size() > 0) {

                //对账
                List<SimpleCreditRecordDto> seqNoList = new ArrayList<>();
                records.forEach(r ->{
                    seqNoList.add(SimpleCreditRecordDto.builder().platformSeqNo(r.getSerialNumber()).build());
                });
                Map<String,Object> requestMap = new HashMap<>();
                requestMap.put("list",seqNoList);
                log.info("compat query credit all url: {}", properties.getCheckUrl());
                log.info("compat query credit all body: {}", JsonHelper.toJson(requestMap));
                Content response = post(properties.getCheckUrl(), JsonHelper.toJson(requestMap));
                log.info("compat query credit all response: {}", response);
                Map<String, CreditBatchQueryDataDto> queryMap = parseQueryResponse(response == null || StringUtils.isEmpty(response.toString()) ? null : response.toString());
                //改变状态
                records.forEach(r -> {
                    if(queryMap == null || queryMap.size() == 0) {
                        r.setSendStatus(CreditStatus.UNKNOWN.name());
                    } else if(queryMap.containsKey(r.getSerialNumber())) {
                        CreditBatchQueryDataDto dataDto = queryMap.get(r.getSerialNumber());
                        CreditStatus status = convertStatus(dataDto.getGrantStatus());
                        r.setSendStatus(status.name());
                        if(status != CreditStatus.FAILED || status != CreditStatus.SUCCESS) {
                            r.setIsComplete(true);
                        }
                    }
                });
                repository.saveAll(records);
            } else {
                hasNext = false;
            }
            page++;
        }
    }

    /**
     * *重发
     * @param startTime
     * @param currentTime
     * @throws Exception
     */
    public void resend(LocalDateTime startTime, LocalDateTime currentTime) throws Exception {
        Date start = DateHelper.toDate(startTime);
        Date end = DateHelper.toDate(currentTime);

        long count = repository.countBySendStatusIsInAndIsCompleteAndSendTimesAndCreateTimeBetween(
                List.of(CreditStatus.SENDING.name(),CreditStatus.UNKNOWN.name()), false, 1, start, end);
        //大于1000条短信预警
        if(count > properties.getWarningSize()) {
            // 发短信
            creditFailure("outNumber");
        }

        int page = 0;
        boolean hasNext = true;
        while (hasNext) {
            //发送中 才需要去对账重发 成功的不需要对账重发
            List<ProjectCqrcbCreditRecord> records = repository.findBySendStatusIsInAndIsCompleteAndSendTimesAndCreateTimeBetween(
                    List.of(CreditStatus.SENDING.name(),CreditStatus.UNKNOWN.name()), false, 1, start, end, PageRequest.of(page, properties.getCheckSize())
            );
            if (CollectionUtils.isNotEmpty(records) && records.size() > 0) {
                //重发
                records.forEach(r ->{
                    resendTask(r);
                });
            } else {
                hasNext = false;
            }
            page++;
        }

    }

    private void resendTask(ProjectCqrcbCreditRecord record) {
        taskProducerHelper.addTask(
                true,
                true,
                false,
                record.getOrgId(),
                null,
                TaskType.WEBHOOK,
                "creditResend", record.getPushId(),
                new TaskWebHookDto(record.getPushId()), null);
    }

    public Content post(String url, String body) throws Exception {
        return Request.Post(url)
                .connectTimeout(1000 * 10)
                .socketTimeout(1000 * 60)
                .version(HttpVersion.HTTP_1_0)
                .setHeader(HTTP.CONN_DIRECTIVE,HTTP.CONN_CLOSE)
                .bodyByteArray(body.getBytes(StandardCharsets.UTF_8), ContentType.APPLICATION_JSON)
                .execute().returnContent();
    }
}
