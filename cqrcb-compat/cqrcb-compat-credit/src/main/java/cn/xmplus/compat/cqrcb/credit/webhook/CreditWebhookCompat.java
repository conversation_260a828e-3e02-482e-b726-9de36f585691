package cn.xmplus.compat.cqrcb.credit.webhook;

import cn.hanyi.cem.compat.webhook.IWebhookCompat;
import cn.hanyi.cem.compat.webhook.IWebhookCompatInfo;
import cn.hanyi.ctm.constant.PushStatus;
import cn.hanyi.ctm.entity.Push;
import cn.xmplus.compat.cqrcb.credit.consts.CreditStatus;
import cn.xmplus.compat.cqrcb.credit.helper.CqrcbCreditHelper;
import cn.xmplus.compat.cqrcb.entity.ProjectCqrcbCreditRecord;
import cn.xmplus.compat.cqrcb.repository.ProjectCqrcbCreditRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class CreditWebhookCompat implements IWebhookCompat {

    @Autowired
    private CqrcbCreditHelper helper;
    @Autowired
    private ProjectCqrcbCreditRecordRepository repository;

    @Override
    public IWebhookCompatInfo hasCompat(Push push) {
        return helper.buildInfo(push);
    }

    @Override
    public PushStatus push(Push push, IWebhookCompatInfo info, SupplierThrowable originPush) {
        CreditStatus status = CreditStatus.FAILED;
        if (info instanceof CreditWebhookCompatInfo) {
            CreditWebhookCompatInfo info1 = ((CreditWebhookCompatInfo) info);
            if (info1.getCreditRecord() != null && info1.getPushDetail() != null) {
                ProjectCqrcbCreditRecord record = info1.getCreditRecord();
                AtomicInteger retry = new AtomicInteger(record.getSendTimes() == null ? 0 : record.getSendTimes());
                status = push(retry, push, info1, originPush);
                record.setSendStatus(status.name());
                record.setIsComplete(false);
                repository.save(record);
            }
        }
        return status.mapToPushStatus();
    }

    private CreditStatus push(AtomicInteger retry, Push push, CreditWebhookCompatInfo info, SupplierThrowable originPush) {
        CreditStatus status = CreditStatus.UNKNOWN;
        ProjectCqrcbCreditRecord record = info.getCreditRecord();
        try {
            push.setRetry(retry.get());
            record.setSendTimes(retry.get());
            String response = originPush.get();
            status = helper.parseResponse(push, info, response);
            log.info("compat send credit status: {}",status);
            record.setResponse(response);
        } catch (Throwable e) {
            record.setResponse(e.getMessage());
            log.error("CreditWebhookCompat push error", e);
        }
        return status;
    }

}
