package cn.xmplus.compat.cqrcb.credit;


import cn.xmplus.compat.cqrcb.credit.helper.CqrcbCreditHelper;
import cn.xmplus.compat.cqrcb.credit.helper.CqrcbEsbXmlHelper;
import cn.xmplus.compat.cqrcb.credit.property.CqrcbCreditProperties;
import cn.xmplus.compat.cqrcb.credit.scheduling.CqrcbCreditScheduling;
import cn.xmplus.compat.cqrcb.credit.webhook.CreditWebhookCompat;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static cn.xmplus.compat.cqrcb.CqrcbCompatCommonConfig.CQRCB_COMPAT_PREFIX;

@Configuration
@EnableConfigurationProperties(CqrcbCreditProperties.class)
@ConditionalOnProperty(prefix = CqrcbCompatCreditConfig.CREDIT, name = "enabled", havingValue = "true")
public class CqrcbCompatCreditConfig {
    public static final String CREDIT = CQRCB_COMPAT_PREFIX + ".credit";
    public static final String SCHEDULING = CREDIT + ".scheduling";

    @Bean
    public CreditWebhookCompat creditWebhookCompat() {
        return new CreditWebhookCompat();
    }

    @Bean
    public CqrcbCreditHelper cqrcbCreditHelper() {
        return new CqrcbCreditHelper();
    }

    @Bean
    public CqrcbEsbXmlHelper CqrcbEsbXmlHelper() {
        return new CqrcbEsbXmlHelper();
    }

    @Bean
    @ConditionalOnProperty(prefix = SCHEDULING, name = "enabled", havingValue = "true")
    public CqrcbCreditScheduling cqrcbCreditScheduling() {
        return new CqrcbCreditScheduling();
    }
}

