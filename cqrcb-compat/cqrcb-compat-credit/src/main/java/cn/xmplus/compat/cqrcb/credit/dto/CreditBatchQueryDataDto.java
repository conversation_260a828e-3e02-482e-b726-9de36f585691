package cn.xmplus.compat.cqrcb.credit.dto;

import cn.hutool.core.date.DateTime;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/10/21 18:09
 */
@Setter
@Getter
public class CreditBatchQueryDataDto {
    private String seqNo;//积分系统流水号
    private String platformSeqNo;//唯一流水号
    private String oriBusinessId;//业务发生流水(第三方)
    private String clientNo;//客户号
    private BigDecimal tranAmt;//实时发放的积分数量
    private String actId;//活动id
    private String prodType;//产品类型
    private String tranNote;//产品类型
    private String grantStatus;//发放状态
    private String sourceChannel;//渠道号
    private String tranType;//交易类型
    private DateTime tranTime;//交易时间
}
