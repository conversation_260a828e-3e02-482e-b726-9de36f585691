package cn.xmplus.compat.cqrcb.credit.helper;

import cn.hutool.core.util.XmlUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Random;
import java.util.TimeZone;

import static org.befun.auth.constant.AuthToastMessage.LOGIN_REQUIRE;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/10/18 16:09
 */
@Slf4j
public class CqrcbEsbXmlHelper {


    /**
     * * 解析响应的xml成map结构
     * @param xml
     * @return
     */
    public Map getEsbResponseBody(String xml) {
        try{
            log.info("send credit esb xml >>>>>> {}", xml);
            String body = "soapenv:Body";
            String response = "ns:Response";
            String responseBody = "ResponseBody";
            Map esbRequest = (Map) XmlUtil.xmlToMap(xml);

            Map bodyMap = (Map) esbRequest.getOrDefault(body, Map.of());
            Map responseMap = (Map) bodyMap.getOrDefault(response, Map.of());
            Map responseBodyMap = (Map) responseMap.getOrDefault(responseBody, Map.of());
            return responseBodyMap;
        }catch (Exception e) {
            log.error("response esb validateEsbRequest error", e);
            throw new AccessDeniedException("解析esb xml响应报文失败！");
        }
    }

    /**
     * *创建esb交易流水号
     * @return
     */
    public String createSerialNumber() {
        //渠道id + 来源(esb) + 交易码(后5位) + 年月时分秒 + 6位序列号
        //09aesb0102520231019
        return "09a" + "esb" + "01025" + getNowStringDate() + createRandomStr(6);
    }

    public static String getNowStringDate() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date();
        formatter.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return formatter.format(date);
    }

    public static String createRandomStr(int length){
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(62);
            stringBuffer.append(str.charAt(number));
        }
        return stringBuffer.toString();
    }
}
